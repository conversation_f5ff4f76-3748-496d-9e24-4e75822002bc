/**
 * 服务器管理相关常量
 *
 * <AUTHOR>
 * @Date 2025-07-10 11:13:02
 */

// -------------- 服务器类型枚举 ------------------------
export const SERVER_TYPE_ENUM = {
  PHYSICAL: {
    value: 0,
    desc: '物理机',
    color: '#2db7f5'
  },
  VIRTUAL: {
    value: 1,
    desc: '虚拟机',
    color: '#87d068'
  }
};

// -------------- 服务器状态枚举 ------------------------
export const SERVER_STATUS_ENUM = {
  RUNNING: {
    value: 'RUNNING',
    desc: '运行中',
    color: '#52c41a'
  },
  STOPPED: {
    value: 'STOPPED',
    desc: '已停止',
    color: '#f5222d'
  },
  MAINTENANCE: {
    value: 'MAINTENANCE',
    desc: '维护中',
    color: '#fa8c16'
  },
  ERROR: {
    value: 'ERROR',
    desc: '异常',
    color: '#eb2f96'
  }
};

// -------------- 使用状态枚举 ------------------------
export const USAGE_STATUS_ENUM = {
  IN_USE: {
    value: 'IN_USE',
    desc: '使用中',
    color: '#52c41a'
  },
  IDLE: {
    value: 'IDLE',
    desc: '空闲',
    color: '#13c2c2'
  },
  RESERVED: {
    value: 'RESERVED',
    desc: '预留',
    color: '#1890ff'
  },
  DECOMMISSIONED: {
    value: 'DECOMMISSIONED',
    desc: '已下线',
    color: '#8c8c8c'
  }
};

// -------------- 同步来源枚举 ------------------------
export const SYNC_SOURCE_ENUM = {
  MANUAL: {
    value: 'MANUAL',
    desc: '手动录入',
    color: '#722ed1'
  },
  XINGYUN: {
    value: 'XINGYUN',
    desc: '行云管家',
    color: '#1890ff'
  },
  VMWARE: {
    value: 'VMWARE',
    desc: 'VMware',
    color: '#52c41a'
  }
};

// -------------- 同步类型枚举 ------------------------
export const SYNC_TYPE_ENUM = {
  MANUAL: {
    value: 'MANUAL',
    desc: '手动同步',
    color: '#1890ff'
  },
  AUTO: {
    value: 'AUTO',
    desc: '自动同步',
    color: '#52c41a'
  }
};

// -------------- 同步状态枚举 ------------------------
export const SYNC_STATUS_ENUM = {
  PROCESSING: {
    value: 0,
    desc: '进行中',
    color: '#1890ff'
  },
  SUCCESS: {
    value: 1,
    desc: '成功',
    color: '#52c41a'
  },
  FAILED: {
    value: 2,
    desc: '失败',
    color: '#f5222d'
  }
};

// -------------- 虚拟机操作类型枚举 ------------------------
export const VM_OPERATION_TYPE_ENUM = {
  START: {
    value: 'START',
    desc: '开机',
    icon: 'PlayCircleOutlined',
    color: '#52c41a'
  },
  STOP: {
    value: 'STOP',
    desc: '关机',
    icon: 'PauseCircleOutlined',
    color: '#f5222d'
  },
  RESTART: {
    value: 'RESTART',
    desc: '重启',
    icon: 'RedoOutlined',
    color: '#fa8c16'
  }
};

// -------------- VMware 电源状态枚举 ------------------------
export const VMWARE_POWER_STATE_ENUM = {
  POWERED_ON: {
    value: 'POWERED_ON',
    desc: '开机',
    color: '#52c41a'
  },
  POWERED_OFF: {
    value: 'POWERED_OFF',
    desc: '关机',
    color: '#f5222d'
  },
  SUSPENDED: {
    value: 'SUSPENDED',
    desc: '暂停',
    color: '#fa8c16'
  }
};

// -------------- 查询表单默认值 ------------------------
export const SERVER_QUERY_FORM_DEFAULT = {
  pageNum: 1,
  pageSize: 10,
  searchCount: true,
  orderBy: 'update_time desc'
};

// -------------- 表格列配置 ------------------------
export const SERVER_TABLE_COLUMNS = [
  {
    title: 'ID',
    dataIndex: 'serverId',
    width: 80,
    fixed: 'left'
  },
  {
    title: '服务器名称',
    dataIndex: 'serverName',
    width: 200,
    ellipsis: true
  },
  {
    title: '主机名',
    dataIndex: 'hostname',
    width: 150,
    ellipsis: true
  },
  {
    title: '类型',
    dataIndex: 'serverType',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'hostStatus',
    width: 100
  },
  {
    title: '操作系统',
    dataIndex: 'operatingSystem',
    width: 150,
    ellipsis: true
  },
  {
    title: '公网IP',
    dataIndex: 'publicIp',
    width: 130
  },
  {
    title: '内网IP',
    dataIndex: 'privateIp',
    width: 130
  },
  {
    title: 'CPU',
    dataIndex: 'cpuCores',
    width: 80
  },
  {
    title: '内存',
    dataIndex: 'memorySize',
    width: 100
  },
  {
    title: '数据来源',
    dataIndex: 'syncSource',
    width: 100
  },
  {
    title: '最后同步',
    dataIndex: 'lastSyncTime',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    fixed: 'right',
    width: 200
  }
];

export default {
  SERVER_TYPE_ENUM,
  SERVER_STATUS_ENUM,
  USAGE_STATUS_ENUM,
  SYNC_SOURCE_ENUM,
  SYNC_TYPE_ENUM,
  SYNC_STATUS_ENUM,
  VM_OPERATION_TYPE_ENUM,
  VMWARE_POWER_STATE_ENUM,
  SERVER_QUERY_FORM_DEFAULT,
  SERVER_TABLE_COLUMNS
};
