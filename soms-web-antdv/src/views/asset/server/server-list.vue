<!--
  * 服务器管理列表页面
  *
  * <AUTHOR>
  * @Date 2025-07-10 11:13:02
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="服务器名称" class="smart-query-form-item">
        <a-input v-model:value="queryForm.serverName" placeholder="请输入服务器名称" allowClear />
      </a-form-item>
      <a-form-item label="服务器类型" class="smart-query-form-item">
        <a-select v-model:value="queryForm.serverType" placeholder="请选择服务器类型" allowClear>
          <a-select-option :value="SERVER_TYPE_ENUM.PHYSICAL.value">{{ SERVER_TYPE_ENUM.PHYSICAL.desc }}</a-select-option>
          <a-select-option :value="SERVER_TYPE_ENUM.VIRTUAL.value">{{ SERVER_TYPE_ENUM.VIRTUAL.desc }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="运行状态" class="smart-query-form-item">
        <a-select v-model:value="queryForm.hostStatus" placeholder="请选择运行状态" allowClear>
          <a-select-option v-for="(item, key) in SERVER_STATUS_ENUM" :key="key" :value="item.value">
            {{ item.desc }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="数据来源" class="smart-query-form-item">
        <a-select v-model:value="queryForm.syncSource" placeholder="请选择数据来源" allowClear>
          <a-select-option v-for="(item, key) in SYNC_SOURCE_ENUM" :key="key" :value="item.value">
            {{ item.desc }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="IP地址" class="smart-query-form-item">
        <a-input v-model:value="queryForm.ipAddress" placeholder="请输入IP地址" allowClear />
      </a-form-item>
      <a-form-item class="smart-query-form-item">
        <a-button type="primary" @click="onSearch">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="resetQuery" class="smart-margin-left10">
          <template #icon>
            <ReloadOutlined />
          </template>
          重置
        </a-button>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button type="primary" @click="handleManualSync">
          <template #icon>
            <SyncOutlined />
          </template>
          手动同步
        </a-button>
        <a-dropdown>
          <template #overlay>
            <a-menu @click="handleSyncBySource">
              <a-menu-item key="XINGYUN">同步行云管家</a-menu-item>
              <a-menu-item key="VMWARE">同步VMware</a-menu-item>
              <a-menu-item key="ALL">同步全部</a-menu-item>
            </a-menu>
          </template>
          <a-button class="smart-margin-left10">
            同步选项
            <DownOutlined />
          </a-button>
        </a-dropdown>
        <a-button type="default" @click="handleBatchDelete" :disabled="selectedRowKeys.length === 0" class="smart-margin-left10">
          <template #icon>
            <DeleteOutlined />
          </template>
          批量删除
        </a-button>
        <a-button type="default" @click="handleViewSyncStatus" class="smart-margin-left10">
          <template #icon>
            <EyeOutlined />
          </template>
          同步状态
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="null" :refresh="queryData" />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <!---------- 表格 begin ----------->
    <a-table
      size="small"
      :scroll="{ x: 1800, y: 600 }"
      :dataSource="tableData"
      :columns="columns"
      rowKey="serverId"
      bordered
      :loading="tableLoading"
      :pagination="false"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
    >
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'serverType'">
          <a-tag :color="getServerTypeColor(text)">
            {{ getServerTypeDesc(text) }}
          </a-tag>
        </template>
        
        <template v-if="column.dataIndex === 'hostStatus'">
          <a-tag :color="getServerStatusColor(text)">
            {{ text || '未知' }}
          </a-tag>
        </template>

        <template v-if="column.dataIndex === 'syncSource'">
          <a-tag :color="getSyncSourceColor(text)">
            {{ getSyncSourceDesc(text) }}
          </a-tag>
        </template>

        <template v-if="column.dataIndex === 'cpuCores'">
          {{ text ? `${text}核` : '-' }}
        </template>

        <template v-if="column.dataIndex === 'memorySize'">
          {{ text ? `${text}GB` : '-' }}
        </template>

        <template v-if="column.dataIndex === 'systemDiskSize'">
          {{ text ? `${text}GB` : '-' }}
        </template>

        <template v-if="column.dataIndex === 'syncStatus'">
          <a-tag :color="text === 0 ? 'green' : 'red'">
            {{ text === 0 ? '正常' : '失败' }}
          </a-tag>
        </template>

        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button type="link" size="small" @click="handleViewDetail(record)">详情</a-button>
            <a-divider type="vertical" />
            <a-dropdown>
              <template #overlay>
                <a-menu @click="({ key }) => handleVmOperation(record, key)">
                  <a-menu-item key="start" :disabled="!canOperate(record)">
                    <PlayCircleOutlined />开机
                  </a-menu-item>
                  <a-menu-item key="stop" :disabled="!canOperate(record)">
                    <PauseCircleOutlined />关机
                  </a-menu-item>
                  <a-menu-item key="restart" :disabled="!canOperate(record)">
                    <RedoOutlined />重启
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button type="link" size="small" :disabled="!canOperate(record)">
                操作 <DownOutlined />
              </a-button>
            </a-dropdown>
            <a-divider type="vertical" />
            <a-button type="link" size="small" @click="handleViewOriginal(record)">原始数据</a-button>
          </div>
        </template>
      </template>
    </a-table>
    <!---------- 表格 end ----------->

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-card>

  <!-- 服务器详情弹窗 -->
  <ServerDetailModal ref="serverDetailModalRef" @refresh="queryData" />

  <!-- 原始数据查看弹窗 -->
  <ServerOriginalDataModal ref="serverOriginalDataModalRef" />
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { 
  SearchOutlined, 
  ReloadOutlined, 
  SyncOutlined, 
  DownOutlined, 
  DeleteOutlined, 
  EyeOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  RedoOutlined
} from '@ant-design/icons-vue';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { serverApi } from '/@/api/asset/server-api';
import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
import { 
  SERVER_TYPE_ENUM,
  SERVER_STATUS_ENUM, 
  SYNC_SOURCE_ENUM,
  SERVER_TABLE_COLUMNS,
  SERVER_QUERY_FORM_DEFAULT
} from '/@/constants/asset/server-const';
import { smartSentry } from '/@/lib/smart-sentry';
import TableOperator from '/@/components/support/table-operator/index.vue';
import ServerDetailModal from './components/server-detail-modal.vue';
import ServerOriginalDataModal from './components/server-original-data-modal.vue';

// -------------- 查询表单相关 ------------------------

// 查询表单默认值
const queryFormState = {
  ...SERVER_QUERY_FORM_DEFAULT,
  serverName: undefined,
  serverType: undefined,
  hostStatus: undefined,
  syncSource: undefined,
  ipAddress: undefined
};

// 查询表单响应式对象
const queryForm = reactive({ ...queryFormState });

/**
 * 重置查询条件
 */
function resetQuery() {
  const pageSize = queryForm.pageSize;
  Object.assign(queryForm, queryFormState);
  queryForm.pageSize = pageSize;
  queryData();
}

/**
 * 执行搜索
 */
function onSearch() {
  queryForm.pageNum = 1;
  queryData();
}

// -------------- 表格相关 ------------------------

// 表格列定义
const columns = ref([...SERVER_TABLE_COLUMNS]);

// 表格加载状态
const tableLoading = ref(false);

// 表格数据
const tableData = ref([]);

// 数据总数
const total = ref(0);

// 选中的行键
const selectedRowKeys = ref([]);

/**
 * 表格行选择变化处理
 */
function onSelectChange(selectedKeys) {
  selectedRowKeys.value = selectedKeys;
}

/**
 * 查询表格数据
 */
async function queryData() {
  tableLoading.value = true;
  try {
    const queryResult = await serverApi.queryPage(queryForm);
    tableData.value = queryResult.data.list;
    total.value = queryResult.data.total;
  } catch (error) {
    smartSentry.captureError(error);
  } finally {
    tableLoading.value = false;
  }
}

// -------------- 同步相关操作 ------------------------

/**
 * 手动同步所有数据源
 */
async function handleManualSync() {
  try {
    SmartLoading.show();
    await serverApi.manualSync('ALL');
    message.success('同步任务已启动，请稍后查看结果');
    queryData();
  } catch (error) {
    smartSentry.captureError(error);
  } finally {
    SmartLoading.hide();
  }
}

/**
 * 按数据源同步
 */
async function handleSyncBySource({ key }) {
  try {
    SmartLoading.show();
    await serverApi.manualSync(key);
    message.success(`${getSyncSourceDesc(key)}同步任务已启动`);
    queryData();
  } catch (error) {
    smartSentry.captureError(error);
  } finally {
    SmartLoading.hide();
  }
}

/**
 * 查看同步状态
 */
async function handleViewSyncStatus() {
  try {
    const result = await serverApi.getSyncStatus();
    // TODO: 显示同步状态详情弹窗
    console.log('同步状态:', result.data);
    message.info('同步状态查看功能开发中');
  } catch (error) {
    smartSentry.captureError(error);
  }
}

// -------------- 服务器操作 ------------------------

// 服务器详情弹窗引用
const serverDetailModalRef = ref();

// 原始数据查看弹窗引用
const serverOriginalDataModalRef = ref();

/**
 * 查看服务器详情
 */
function handleViewDetail(record) {
  serverDetailModalRef.value?.showModal(record.serverId);
}

/**
 * 查看原始数据
 */
function handleViewOriginal(record) {
  serverOriginalDataModalRef.value?.showModal(record.serverId);
}

/**
 * 判断是否可以操作虚拟机
 */
function canOperate(record) {
  return record.syncSource === 'VMWARE' && record.serverType === 1;
}

/**
 * 虚拟机操作
 */
async function handleVmOperation(record, operation) {
  Modal.confirm({
    title: '确认操作',
    content: `确定要对虚拟机 ${record.serverName} 执行${getOperationDesc(operation)}操作吗？`,
    async onOk() {
      try {
        SmartLoading.show();
        let result;
        switch (operation) {
          case 'start':
            result = await serverApi.startVm(record.serverId);
            break;
          case 'stop':
            result = await serverApi.stopVm(record.serverId);
            break;
          case 'restart':
            result = await serverApi.restartVm(record.serverId);
            break;
        }
        if (result.data) {
          message.success(`${getOperationDesc(operation)}操作执行成功`);
          queryData();
        } else {
          message.error(`${getOperationDesc(operation)}操作执行失败`);
        }
      } catch (error) {
        smartSentry.captureError(error);
      } finally {
        SmartLoading.hide();
      }
    }
  });
}

/**
 * 批量删除服务器
 */
function handleBatchDelete() {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的服务器');
    return;
  }
  
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 台服务器吗？`,
    async onOk() {
      try {
        SmartLoading.show();
        await serverApi.batchDelete(selectedRowKeys.value);
        message.success('删除成功');
        selectedRowKeys.value = [];
        queryData();
      } catch (error) {
        smartSentry.captureError(error);
      } finally {
        SmartLoading.hide();
      }
    }
  });
}

// -------------- 辅助方法 ------------------------

/**
 * 获取服务器类型描述
 */
function getServerTypeDesc(value) {
  const typeItem = Object.values(SERVER_TYPE_ENUM).find(item => item.value === value);
  return typeItem?.desc || '未知';
}

/**
 * 获取服务器类型颜色
 */
function getServerTypeColor(value) {
  const typeItem = Object.values(SERVER_TYPE_ENUM).find(item => item.value === value);
  return typeItem?.color || '#d9d9d9';
}

/**
 * 获取服务器状态颜色
 */
function getServerStatusColor(value) {
  const statusItem = Object.values(SERVER_STATUS_ENUM).find(item => item.value === value);
  return statusItem?.color || '#d9d9d9';
}

/**
 * 获取同步来源描述
 */
function getSyncSourceDesc(value) {
  const sourceItem = Object.values(SYNC_SOURCE_ENUM).find(item => item.value === value);
  return sourceItem?.desc || '未知';
}

/**
 * 获取同步来源颜色
 */
function getSyncSourceColor(value) {
  const sourceItem = Object.values(SYNC_SOURCE_ENUM).find(item => item.value === value);
  return sourceItem?.color || '#d9d9d9';
}

/**
 * 获取操作描述
 */
function getOperationDesc(operation) {
  const operationMap = {
    start: '开机',
    stop: '关机',
    restart: '重启'
  };
  return operationMap[operation] || operation;
}

// 页面挂载时执行查询
onMounted(queryData);
</script>

<style scoped>
.smart-table-operate {
  white-space: nowrap;
}
</style>
