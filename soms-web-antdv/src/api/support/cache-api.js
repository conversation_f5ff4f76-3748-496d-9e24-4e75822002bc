/*
 * 缓存
 *
 * @Author:    罗伊
 * @Date:      2022-09-03 21:51:34
 * @Wechat:    zhuda1024
 * @Email:     <EMAIL>
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */
import {getRequest} from '/@/lib/axios';

export const cacheApi = {
  // 获取某个缓存的所有key <AUTHOR>
  getKeys: (cacheName) => {
    return getRequest(`/support/cache/keys/${cacheName}`);
  },
  // 移除某个缓存 <AUTHOR>
  remove: (cacheName) => {
    return getRequest(`/support/cache/remove/${cacheName}`);
  },
  // 获取所有缓存 <AUTHOR>
  getAllCacheNames: () => {
    return getRequest('/support/cache/names');
  },
};
