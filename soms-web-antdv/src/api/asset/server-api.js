/**
 * 服务器管理 api 封装
 *
 * <AUTHOR>
 * @Date 2025-07-10 11:13:02
 */
import { getRequest, postRequest } from '/@/lib/axios';

export const serverApi = {

  /**
   * 分页查询服务器列表
   * @param params 查询参数
   * @returns Promise
   * <AUTHOR>
   */
  queryPage: (params) => {
    return postRequest('/server/page/query', params);
  },

  /**
   * 根据ID查询服务器详情
   * @param serverId 服务器ID
   * @returns Promise
   * <AUTHOR>
   */
  queryById: (serverId) => {
    return getRequest(`/server/detail/${serverId}`);
  },

  /**
   * 手动同步服务器数据
   * @param syncSource 同步来源：XINGYUN-行云管家，VMWARE-VMware，ALL-全部
   * @returns Promise
   * <AUTHOR>
   */
  manualSync: (syncSource = 'ALL') => {
    return postRequest(`/server/sync/manual?syncSource=${syncSource}`);
  },

  /**
   * 获取服务器原始数据
   * @param serverId 服务器ID
   * @returns Promise
   * <AUTHOR>
   */
  getOriginalData: (serverId) => {
    return getRequest(`/server/originalData/${serverId}`);
  },

  /**
   * 虚拟机开机
   * @param serverId 服务器ID
   * @returns Promise
   * <AUTHOR>
   */
  startVm: (serverId) => {
    return postRequest(`/server/vm/start/${serverId}`);
  },

  /**
   * 虚拟机关机
   * @param serverId 服务器ID
   * @returns Promise
   * <AUTHOR>
   */
  stopVm: (serverId) => {
    return postRequest(`/server/vm/stop/${serverId}`);
  },

  /**
   * 虚拟机重启
   * @param serverId 服务器ID
   * @returns Promise
   * <AUTHOR>
   */
  restartVm: (serverId) => {
    return postRequest(`/server/vm/restart/${serverId}`);
  },

  /**
   * 获取虚拟机电源状态
   * @param serverId 服务器ID
   * @returns Promise
   * <AUTHOR>
   */
  getVmPowerState: (serverId) => {
    return getRequest(`/server/vm/powerState/${serverId}`);
  },

  /**
   * 批量删除服务器
   * @param serverIds 服务器ID列表
   * @returns Promise
   * <AUTHOR>
   */
  batchDelete: (serverIds) => {
    return postRequest('/server/batch/delete', serverIds);
  },

  /**
   * 获取同步状态统计
   * @returns Promise
   * <AUTHOR>
   */
  getSyncStatistics: () => {
    return getRequest('/server/sync/statistics');
  },

  /**
   * 获取同步日志详情
   * @param logId 日志ID
   * @returns Promise
   * <AUTHOR>
   */
  getSyncLogDetail: (logId) => {
    return getRequest(`/server/sync/log/${logId}`);
  },

  /**
   * 获取同步状态
   * @returns Promise
   * <AUTHOR>
   */
  getSyncStatus: () => {
    return getRequest('/server/sync/status');
  }

};
