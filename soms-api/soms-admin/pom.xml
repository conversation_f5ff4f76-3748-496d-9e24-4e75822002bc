<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.edu.xmut</groupId>
        <artifactId>soms-parent</artifactId>
        <version>3.0.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>soms-admin</artifactId>
    <version>3.0.0</version>
    <packaging>jar</packaging>

    <name>soms-admin</name>
    <description>soms-admin project</description>

    <dependencies>

        <dependency>
            <groupId>cn.edu.xmut</groupId>
            <artifactId>soms-base</artifactId>
            <version>3.0.0</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>cn.edu.xmut.soms.admin.AdminApplication</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                                                    </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>