<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.edu.xmut.soms.admin.module.asset.server.dao.ServerDao">

    <!-- 查询结果列 -->
    <sql id="base_columns">
        t_server.server_id,
        t_server.server_type,
        t_server.server_name,
        t_server.server_status,
        t_server.public_ip,
        t_server.private_ip,
        t_server.cpu_cores,
        t_server.memory_size,
        t_server.disk_size,
        t_server.create_time,
        t_server.update_time
    </sql>

    <!-- 分页查询 -->
    <select id="queryPage" resultType="cn.edu.xmut.soms.admin.module.asset.server.domain.vo.ServerVO">
        SELECT
        <include refid="base_columns"/>
        FROM t_server
    </select>


</mapper>
