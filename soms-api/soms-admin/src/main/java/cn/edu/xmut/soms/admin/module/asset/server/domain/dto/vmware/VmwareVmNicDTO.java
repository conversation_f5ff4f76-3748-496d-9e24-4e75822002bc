package cn.edu.xmut.soms.admin.module.asset.server.domain.dto.vmware;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * VMware 虚拟机网络适配器信息 DTO
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VmwareVmNicDTO {

    /**
     * 网络适配器标签
     */
    private String label;

    /**
     * 网络适配器类型
     */
    private String type;

    /**
     * UPT 兼容性启用标志
     */
    private Boolean uptCompatibilityEnabled;

    /**
     * MAC 地址类型
     */
    private String macType;

    /**
     * MAC 地址
     */
    private String macAddress;

    /**
     * PCI 插槽编号
     */
    private Integer pciSlotNumber;

    /**
     * 网络唤醒启用标志
     */
    private Boolean wakeOnLanEnabled;

    /**
     * 网络适配器后端配置
     */
    private VmwareVmNicBackingDTO backing;

    /**
     * 连接状态
     */
    private String state;

    /**
     * 启动时连接标志
     */
    private Boolean startConnected;

    /**
     * 允许客户机控制标志
     */
    private Boolean allowGuestControl;

    /**
     * 网络适配器后端配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VmwareVmNicBackingDTO {
        /**
         * 后端类型
         */
        private String type;

        /**
         * 网络 ID
         */
        private String network;

        /**
         * 网络名称
         */
        private String networkName;

        /**
         * 主机设备
         */
        private String hostDevice;

        /**
         * 分布式交换机 UUID
         */
        private String distributedSwitchUuid;

        /**
         * 分布式端口
         */
        private String distributedPort;

        /**
         * 连接 Cookie
         */
        private Long connectionCookie;

        /**
         * 不透明网络类型
         */
        private String opaqueNetworkType;

        /**
         * 不透明网络 ID
         */
        private String opaqueNetworkId;
    }
}
