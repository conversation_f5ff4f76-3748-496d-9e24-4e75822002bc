package cn.edu.xmut.soms.admin.module.asset.server.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.edu.xmut.soms.admin.module.asset.server.service.ServerSyncLogService;
import cn.edu.xmut.soms.base.common.domain.ResponseDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

/**
 * 服务器同步日志管理 Controller
 *
 * <AUTHOR>
 * @Date 2025-07-12
 */

@RestController
@Tag(name = "服务器同步日志管理")
public class ServerSyncLogController {

    @Resource
    private ServerSyncLogService serverSyncLogService;

    @Operation(summary = "获取同步日志详情 <AUTHOR>
    @GetMapping("/server/sync/log/{logId}")
    @SaCheckPermission("server:query")
    public ResponseDTO<Object> getSyncLogDetail(@Parameter(description = "同步日志ID") @PathVariable Long logId) {
        return ResponseDTO.ok(serverSyncLogService.getSyncLogDetail(logId));
    }

    @Operation(summary = "获取同步状态 <AUTHOR>
    @GetMapping("/server/sync/status")
    @SaCheckPermission("server:query")
    public ResponseDTO<Object> getSyncStatus() {
        return ResponseDTO.ok(serverSyncLogService.getCurrentSyncStatus());
    }

}