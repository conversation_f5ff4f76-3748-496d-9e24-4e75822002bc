package cn.edu.xmut.soms.admin.module.asset.server.constant;

import cn.edu.xmut.soms.base.common.enumeration.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * VMware 虚拟机电源状态枚举
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@AllArgsConstructor
@Getter
public enum VmwarePowerStateEnum implements BaseEnum {
    /**
     * 已关机
     */
    POWERED_OFF(1, "POWERED_OFF", "已关机"),

    /**
     * 已开机
     */
    POWERED_ON(2, "POWERED_ON", "已开机"),

    /**
     * 已挂起
     */
    SUSPENDED(3, "SUSPENDED", "已挂起"),
    ;

    private final Integer value;

    private final String code;

    private final String desc;
}
