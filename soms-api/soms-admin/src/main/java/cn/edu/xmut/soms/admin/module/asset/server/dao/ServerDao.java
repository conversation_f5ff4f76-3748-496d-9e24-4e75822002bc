package cn.edu.xmut.soms.admin.module.asset.server.dao;

import cn.edu.xmut.soms.admin.module.asset.server.domain.entity.ServerEntity;
import cn.edu.xmut.soms.admin.module.asset.server.domain.form.ServerQueryForm;
import cn.edu.xmut.soms.admin.module.asset.server.domain.vo.ServerVO;
import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 服务器信息表 Dao
 *
 * <AUTHOR>
 * @Date 2025-07-10 11:13:02
 * @Copyright xmut
 */

@Mapper
public interface ServerDao extends BaseMapper<ServerEntity> {

    /**
     * 分页 查询
     *
     * @param page
     * @param queryForm
     * @return
     */
    List<ServerVO> queryPage(Page page, @Param("queryForm") ServerQueryForm queryForm);

}
