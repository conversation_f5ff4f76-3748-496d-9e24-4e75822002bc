package cn.edu.xmut.soms.admin.module.asset.server.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 服务器信息表 实体类
 *
 * <AUTHOR>
 * @Date 2025-07-10 11:13:02
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_server")
public class ServerEntity {

    /**
     * 服务器唯一标识
     */
    @TableId(type = IdType.AUTO)
    private Long serverId;

    /**
     * 服务器类型：0-物理机，1-虚拟机
     */
    private Integer serverType;

    /**
     * 操作系统信息
     */
    private String operatingSystem;

    /**
     * 公网IP地址
     */
    private String publicIp;

    /**
     * 内网IP地址
     */
    private String privateIp;

    /**
     * 主机运行状态
     */
    private String hostStatus;

    /**
     * CPU核心数量
     */
    private Integer cpuCores;

    /**
     * 内存大小，单位GB
     */
    private BigDecimal memorySize;

    /**
     * 主机描述信息
     */
    private String hostDescription;

    /**
     * 系统盘大小，单位GB
     */
    private BigDecimal systemDiskSize;

    /**
     * 数据盘大小，单位GB
     */
    private BigDecimal dataDiskSize;

    /**
     * 逻辑删除标识：0-未删除，1-已删除
     */
    @TableLogic
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除时间
     */
    private LocalDateTime deleteTime;

    /**
     * 是否关联信息系统：0-否，1-是
     */
    private Integer isLinkedSystem;

    /**
     * 是否可访问互联网：0-否，1-是
     */
    private Integer internetAccess;

    /**
     * 使用状态
     */
    private String usageStatus;

    /**
     * 服务器用途描述
     */
    private String purpose;

    /**
     * IP地址列表，JSON格式存储
     */
    private String ipList;

    /**
     * 手动录入的IP地址
     */
    private String manualIp;

    /**
     * NAT映射IP地址
     */
    private String natIp;

    /**
     * 主机名
     */
    private String hostname;

    /**
     * 服务器名称
     */
    private String serverName;

    /**
     * 服务器位置
     */
    private String location;

    /**
     * 所属部门ID
     */
    private Long departmentId;

    /**
     * 负责人
     */
    private String responsiblePerson;

    /**
     * 联系方式
     */
    private String contactInfo;

    /**
     * 采购日期
     */
    private LocalDate purchaseDate;

    /**
     * 保修到期日期
     */
    private LocalDate warrantyDate;

    /**
     * 厂商
     */
    private String vendor;

    /**
     * 型号
     */
    private String model;

    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 资产编号
     */
    private String assetNumber;

    /**
     * 数据来源：MANUAL-手动录入，XINGYUN-行云管家，VMWARE-VMware
     */
    private String syncSource;

    /**
     * 外部系统ID
     */
    private String externalId;

    /**
     * 最后同步时间
     */
    private LocalDateTime lastSyncTime;

    /**
     * 同步状态：0-正常，1-同步失败
     */
    private Integer syncStatus;

    /**
     * 同步错误信息
     */
    private String syncErrorMsg;

}
