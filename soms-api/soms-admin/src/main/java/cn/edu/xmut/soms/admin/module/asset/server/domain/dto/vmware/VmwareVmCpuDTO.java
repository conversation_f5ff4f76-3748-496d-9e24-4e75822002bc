package cn.edu.xmut.soms.admin.module.asset.server.domain.dto.vmware;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * VMware 虚拟机 CPU 信息 DTO
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VmwareVmCpuDTO {

    /**
     * CPU 数量
     */
    private Integer count;

    /**
     * 每个插槽的核心数
     */
    private Integer coresPerSocket;

    /**
     * 热添加启用标志
     */
    private Boolean hotAddEnabled;

    /**
     * 热移除启用标志
     */
    private Boolean hotRemoveEnabled;
}
