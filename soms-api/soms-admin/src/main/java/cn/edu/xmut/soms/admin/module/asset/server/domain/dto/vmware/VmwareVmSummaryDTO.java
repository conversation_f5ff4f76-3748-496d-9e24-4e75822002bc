package cn.edu.xmut.soms.admin.module.asset.server.domain.dto.vmware;

import cn.edu.xmut.soms.admin.module.asset.server.constant.VmwarePowerStateEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/7/11 15:15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VmwareVmSummaryDTO {

    /**
     * 虚拟机 ID
     */
    private String vm;

    /**
     * 虚拟机名称
     */
    private String name;

    /**
     * 电源状态
     */
    private String powerState;

    /**
     * CPU 核心数
     */
    private Integer cpuCount;

    /**
     * 内存大小，单位为 MiB
     */
    private Integer memorySizeMiB;
}
