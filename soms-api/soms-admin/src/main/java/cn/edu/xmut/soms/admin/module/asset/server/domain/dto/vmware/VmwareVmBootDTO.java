package cn.edu.xmut.soms.admin.module.asset.server.domain.dto.vmware;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * VMware 虚拟机启动信息 DTO
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VmwareVmBootDTO {

    /**
     * 启动类型
     */
    private String type;

    /**
     * EFI 传统启动标志
     */
    private Boolean efiLegacyBoot;

    /**
     * 网络协议
     */
    private String networkProtocol;

    /**
     * 延迟时间（毫秒）
     */
    private Long delay;

    /**
     * 重试标志
     */
    private Boolean retry;

    /**
     * 重试延迟时间（毫秒）
     */
    private Long retryDelay;

    /**
     * 进入设置模式标志
     */
    private Boolean enterSetupMode;
}
