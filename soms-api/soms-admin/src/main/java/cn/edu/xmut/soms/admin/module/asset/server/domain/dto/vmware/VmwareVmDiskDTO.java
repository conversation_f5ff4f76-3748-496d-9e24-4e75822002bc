package cn.edu.xmut.soms.admin.module.asset.server.domain.dto.vmware;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * VMware 虚拟机磁盘信息 DTO
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VmwareVmDiskDTO {

    /**
     * 磁盘标签
     */
    private String label;

    /**
     * 磁盘类型
     */
    private String type;

    /**
     * IDE 地址信息
     */
    private VmwareVmDiskIdeAddressDTO ide;

    /**
     * SCSI 地址信息
     */
    private VmwareVmDiskScsiAddressDTO scsi;

    /**
     * SATA 地址信息
     */
    private VmwareVmDiskSataAddressDTO sata;

    /**
     * NVMe 地址信息
     */
    private VmwareVmDiskNvmeAddressDTO nvme;

    /**
     * 磁盘后端配置
     */
    private VmwareVmDiskBackingDTO backing;

    /**
     * 磁盘容量（字节）
     */
    private Long capacity;

    /**
     * IDE 地址信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VmwareVmDiskIdeAddressDTO {
        /**
         * 是否为主通道
         */
        private Boolean primary;

        /**
         * 是否为主设备
         */
        private Boolean master;
    }

    /**
     * SCSI 地址信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VmwareVmDiskScsiAddressDTO {
        /**
         * 总线编号
         */
        private Integer bus;

        /**
         * 单元编号
         */
        private Integer unit;
    }

    /**
     * SATA 地址信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VmwareVmDiskSataAddressDTO {
        /**
         * 总线编号
         */
        private Integer bus;

        /**
         * 单元编号
         */
        private Integer unit;
    }

    /**
     * NVMe 地址信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VmwareVmDiskNvmeAddressDTO {
        /**
         * 总线编号
         */
        private Integer bus;

        /**
         * 单元编号
         */
        private Integer unit;
    }

    /**
     * 磁盘后端配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VmwareVmDiskBackingDTO {
        /**
         * 后端类型
         */
        private String type;

        /**
         * VMDK 文件路径
         */
        private String vmdkFile;
    }
}
