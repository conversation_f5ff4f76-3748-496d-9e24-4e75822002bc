package cn.edu.xmut.soms.admin.module.asset.server.constant;

import cn.edu.xmut.soms.base.common.enumeration.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * VMware 虚拟机客户操作系统枚举
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@AllArgsConstructor
@Getter
public enum VmwareGuestOSEnum implements BaseEnum {
    /**
     * MS-DOS
     */
    DOS(1, "DOS", "MS-DOS"),

    /**
     * Windows 3.1
     */
    WIN_31(2, "WIN_31", "Windows 3.1"),

    /**
     * Windows 95
     */
    WIN_95(3, "WIN_95", "Windows 95"),

    /**
     * Windows 98
     */
    WIN_98(4, "WIN_98", "Windows 98"),

    /**
     * Windows ME
     */
    WIN_ME(5, "WIN_ME", "Windows Millennium Edition"),

    /**
     * Windows NT 4
     */
    WIN_NT(6, "WIN_NT", "Windows NT 4"),

    /**
     * Windows 2000 Professional
     */
    WIN_2000_PRO(7, "WIN_2000_PRO", "Windows 2000 Professional"),

    /**
     * Windows 2000 Server
     */
    WIN_2000_SERV(8, "WIN_2000_SERV", "Windows 2000 Server"),

    /**
     * Windows 2000 Advanced Server
     */
    WIN_2000_ADV_SERV(9, "WIN_2000_ADV_SERV", "Windows 2000 Advanced Server"),

    /**
     * Windows XP Home Edition
     */
    WIN_XP_HOME(10, "WIN_XP_HOME", "Windows XP Home Edition"),

    /**
     * Windows XP Professional
     */
    WIN_XP_PRO(11, "WIN_XP_PRO", "Windows XP Professional"),

    /**
     * Windows XP Professional (64 bit)
     */
    WIN_XP_PRO_64(12, "WIN_XP_PRO_64", "Windows XP Professional (64 bit)"),

    /**
     * Windows Server 2003, Web Edition
     */
    WIN_NET_WEB(13, "WIN_NET_WEB", "Windows Server 2003, Web Edition"),

    /**
     * Windows Server 2003, Standard Edition
     */
    WIN_NET_STANDARD(14, "WIN_NET_STANDARD", "Windows Server 2003, Standard Edition"),

    /**
     * Windows Server 2003, Enterprise Edition
     */
    WIN_NET_ENTERPRISE(15, "WIN_NET_ENTERPRISE", "Windows Server 2003, Enterprise Edition"),

    /**
     * Windows Server 2003, Datacenter Edition
     */
    WIN_NET_DATACENTER(16, "WIN_NET_DATACENTER", "Windows Server 2003, Datacenter Edition"),

    /**
     * Windows Small Business Server 2003
     */
    WIN_NET_BUSINESS(17, "WIN_NET_BUSINESS", "Windows Small Business Server 2003"),

    /**
     * Windows Server 2003, Standard Edition (64 bit)
     */
    WIN_NET_STANDARD_64(18, "WIN_NET_STANDARD_64", "Windows Server 2003, Standard Edition (64 bit)"),

    /**
     * Windows Server 2003, Enterprise Edition (64 bit)
     */
    WIN_NET_ENTERPRISE_64(19, "WIN_NET_ENTERPRISE_64", "Windows Server 2003, Enterprise Edition (64 bit)"),

    /**
     * Windows Vista
     */
    WIN_VISTA(20, "WIN_VISTA", "Windows Vista"),

    /**
     * Windows Vista (64 bit)
     */
    WIN_VISTA_64(21, "WIN_VISTA_64", "Windows Vista (64 bit)"),

    /**
     * Windows 7
     */
    WINDOWS_7(22, "WINDOWS_7", "Windows 7"),

    /**
     * Windows 7 (64 bit)
     */
    WINDOWS_7_64(23, "WINDOWS_7_64", "Windows 7 (64 bit)"),

    /**
     * Windows Server 2008 R2 (64 bit)
     */
    WINDOWS_7_SERVER_64(24, "WINDOWS_7_SERVER_64", "Windows Server 2008 R2 (64 bit)"),

    /**
     * Windows 8
     */
    WINDOWS_8(25, "WINDOWS_8", "Windows 8"),

    /**
     * Windows 8 (64 bit)
     */
    WINDOWS_8_64(26, "WINDOWS_8_64", "Windows 8 (64 bit)"),

    /**
     * Windows 8 Server (64 bit)
     */
    WINDOWS_8_SERVER_64(27, "WINDOWS_8_SERVER_64", "Windows 8 Server (64 bit)"),

    /**
     * Windows 10
     */
    WINDOWS_9(28, "WINDOWS_9", "Windows 10"),

    /**
     * Windows 10 (64 bit)
     */
    WINDOWS_9_64(29, "WINDOWS_9_64", "Windows 10 (64 bit)"),

    /**
     * Windows 10 Server (64 bit)
     */
    WINDOWS_9_SERVER_64(30, "WINDOWS_9_SERVER_64", "Windows 10 Server (64 bit)"),

    /**
     * Windows Hyper-V
     */
    WINDOWS_HYPERV(31, "WINDOWS_HYPERV", "Windows Hyper-V"),

    /**
     * Windows Server 2019
     */
    WINDOWS_SERVER_2019(32, "WINDOWS_SERVER_2019", "Windows Server 2019"),

    /**
     * Windows Server 2022
     */
    WINDOWS_SERVER_2021(33, "WINDOWS_SERVER_2021", "Windows Server 2022"),

    /**
     * FreeBSD 10 or earlier
     */
    FREEBSD(34, "FREEBSD", "FreeBSD 10 or earlier"),

    /**
     * FreeBSD 10 x64 or earlier
     */
    FREEBSD_64(35, "FREEBSD_64", "FreeBSD 10 x64 or earlier"),

    /**
     * FreeBSD 11
     */
    FREEBSD_11(36, "FREEBSD_11", "FreeBSD 11"),

    /**
     * FreeBSD 12
     */
    FREEBSD_12(37, "FREEBSD_12", "FreeBSD 12"),

    /**
     * FreeBSD 13 or later
     */
    FREEBSD_13(38, "FREEBSD_13", "FreeBSD 13 or later"),

    /**
     * Linux
     */
    LINUX(39, "LINUX", "Linux"),

    /**
     * Ubuntu Linux
     */
    UBUNTU_LINUX(40, "UBUNTU_LINUX", "Ubuntu Linux"),

    /**
     * CentOS Linux
     */
    CENTOS_LINUX(41, "CENTOS_LINUX", "CentOS Linux"),

    /**
     * Red Hat Enterprise Linux
     */
    RHEL_LINUX(42, "RHEL_LINUX", "Red Hat Enterprise Linux"),

    /**
     * SUSE Linux Enterprise Server
     */
    SLES_LINUX(43, "SLES_LINUX", "SUSE Linux Enterprise Server"),

    /**
     * Oracle Linux
     */
    ORACLE_LINUX(44, "ORACLE_LINUX", "Oracle Linux"),

    /**
     * 其他操作系统
     */
    OTHER(999, "OTHER", "其他操作系统"),
    ;

    private final Integer value;

    private final String code;

    private final String desc;
}
