package cn.edu.xmut.soms.admin.module.asset.server.service;

import cn.edu.xmut.soms.admin.module.asset.server.constant.ServerSyncSourceEnum;
import cn.edu.xmut.soms.admin.module.asset.server.constant.ServerSyncTypeEnum;
import cn.edu.xmut.soms.admin.module.asset.server.dao.ServerDao;
import cn.edu.xmut.soms.admin.module.asset.server.domain.dto.cloudbility.CloudHostResultDTO;
import cn.edu.xmut.soms.admin.module.asset.server.domain.dto.vmware.VmwareVmInfoDTO;
import cn.edu.xmut.soms.admin.module.asset.server.domain.entity.ServerEntity;
import cn.edu.xmut.soms.admin.module.asset.server.domain.form.ServerQueryForm;
import cn.edu.xmut.soms.admin.module.asset.server.domain.vo.ServerVO;
import cn.edu.xmut.soms.admin.module.asset.server.manager.CloudbilityManager;
import cn.edu.xmut.soms.admin.module.asset.server.manager.VMwareManager;
import cn.edu.xmut.soms.admin.module.system.datascope.DataScope;
import cn.edu.xmut.soms.admin.module.system.datascope.constant.DataScopeTypeEnum;
import cn.edu.xmut.soms.base.common.domain.PageResult;
import cn.edu.xmut.soms.base.common.domain.ResponseDTO;
import cn.edu.xmut.soms.base.common.util.SmartPageUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 服务器信息表 Service
 *
 * <AUTHOR>
 * @Date 2025-07-10 11:13:02
 */

@Slf4j
@Service
public class ServerService {

    @Resource
    private ServerDao serverDao;

    @Resource
    private CloudbilityManager cloudbilityManager;

    @Resource
    private VMwareManager vmwareManager;

    @Resource
    private ServerSyncService serverSyncService;

    @Resource
    private VmOperationService vmOperationService;

    /**
     * 分页查询（带数据范围权限控制）
     */
    @DataScope(dataScopeType = DataScopeTypeEnum.DEPT_AND_SUB_DEPT)
    public PageResult<ServerVO> queryPage(ServerQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<ServerVO> list = serverDao.queryPage(page, queryForm);
        return SmartPageUtil.convert2PageResult(page, list);
    }

    /**
     * 根据ID查询服务器详情
     *
     * @param serverId 服务器ID
     * @return 服务器详情
     */
    public ServerVO queryById(Long serverId) {
        ServerEntity serverEntity = serverDao.selectById(serverId);
        if (serverEntity == null) {
            throw new RuntimeException("服务器不存在");
        }
        
        // 转换为VO
        return convertToVO(serverEntity);
    }

    /**
     * 手动同步服务器数据
     *
     * @param syncSource 同步来源
     * @return 同步结果
     */
    public ResponseDTO<Map<String, Long>> manualSyncServer(String syncSource) {
        try {
            Map<String, Long> result;
            
            if (ServerSyncSourceEnum.XINGYUN.getValue().equals(syncSource)) {
                Long logId = serverSyncService.syncCloudbilityData(ServerSyncTypeEnum.MANUAL);
                result = Map.of("cloudbilityLogId", logId);
            } else if (ServerSyncSourceEnum.VMWARE.getValue().equals(syncSource)) {
                Long logId = serverSyncService.syncVmwareData(ServerSyncTypeEnum.MANUAL);
                result = Map.of("vmwareLogId", logId);
            } else {
                result = serverSyncService.syncAllData(ServerSyncTypeEnum.MANUAL);
            }
            
            return ResponseDTO.ok(result);
        } catch (Exception e) {
            log.error("手动同步服务器数据失败", e);
            return ResponseDTO.userErrorParam("同步失败: " + e.getMessage());
        }
    }

    /**
     * 获取服务器原始数据（行云管家或VMware）
     *
     * @param serverId 服务器ID
     * @return 原始数据
     */
    public ResponseDTO<Object> getServerOriginalData(Long serverId) {
        try {
            ServerEntity server = serverDao.selectById(serverId);
            if (server == null) {
                return ResponseDTO.userErrorParam("服务器不存在");
            }
            
            String syncSource = server.getSyncSource();
            String externalId = server.getExternalId();
            
            if (ServerSyncSourceEnum.XINGYUN.getValue().equals(syncSource)) {
                // 获取行云管家原始数据
                CloudHostResultDTO cloudData = cloudbilityManager.getTeamHostsAsDTO();
                return ResponseDTO.ok(cloudData);
            } else if (ServerSyncSourceEnum.VMWARE.getValue().equals(syncSource)) {
                // 获取VMware原始数据
                String clusterName = server.getLocation();
                if (clusterName != null) {
                    VmwareVmInfoDTO vmInfo = vmwareManager.getVmDetailInfo(clusterName, externalId);
                    return ResponseDTO.ok(vmInfo);
                }
            }
            
            return ResponseDTO.userErrorParam("无法获取原始数据");
        } catch (Exception e) {
            log.error("获取服务器原始数据失败", e);
            return ResponseDTO.userErrorParam("获取原始数据失败: " + e.getMessage());
        }
    }

    /**
     * 虚拟机开机
     *
     * @param serverId 服务器ID
     * @return 操作结果
     */
    public ResponseDTO<Boolean> startVm(Long serverId) {
        try {
            boolean result = vmOperationService.startVm(serverId);
            return ResponseDTO.ok(result);
        } catch (Exception e) {
            log.error("虚拟机开机操作失败", e);
            return ResponseDTO.userErrorParam("开机失败: " + e.getMessage());
        }
    }

    /**
     * 虚拟机关机
     *
     * @param serverId 服务器ID
     * @return 操作结果
     */
    public ResponseDTO<Boolean> stopVm(Long serverId) {
        try {
            boolean result = vmOperationService.stopVm(serverId);
            return ResponseDTO.ok(result);
        } catch (Exception e) {
            log.error("虚拟机关机操作失败", e);
            return ResponseDTO.userErrorParam("关机失败: " + e.getMessage());
        }
    }

    /**
     * 虚拟机重启
     *
     * @param serverId 服务器ID
     * @return 操作结果
     */
    public ResponseDTO<Boolean> restartVm(Long serverId) {
        try {
            boolean result = vmOperationService.restartVm(serverId);
            return ResponseDTO.ok(result);
        } catch (Exception e) {
            log.error("虚拟机重启操作失败", e);
            return ResponseDTO.userErrorParam("重启失败: " + e.getMessage());
        }
    }

    /**
     * 获取虚拟机电源状态
     *
     * @param serverId 服务器ID
     * @return 电源状态
     */
    public ResponseDTO<String> getVmPowerState(Long serverId) {
        try {
            String powerState = vmOperationService.getVmPowerState(serverId);
            return ResponseDTO.ok(powerState);
        } catch (Exception e) {
            log.error("获取虚拟机电源状态失败", e);
            return ResponseDTO.userErrorParam("获取状态失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除服务器
     *
     * @param serverIds 服务器ID列表
     * @return 删除结果
     */
    public ResponseDTO<Void> batchDelete(List<Long> serverIds) {
        try {
            if (serverIds == null || serverIds.isEmpty()) {
                return ResponseDTO.userErrorParam("请选择要删除的服务器");
            }
            
            serverDao.deleteBatchIds(serverIds);
            return ResponseDTO.ok();
        } catch (Exception e) {
            log.error("批量删除服务器失败", e);
            return ResponseDTO.userErrorParam("删除失败: " + e.getMessage());
        }
    }

    /**
     * 获取同步状态统计
     *
     * @return 同步状态统计
     */
    public ResponseDTO<Map<String, Object>> getSyncStatistics() {
        // TODO: 实现同步状态统计
        return ResponseDTO.ok(Map.of());
    }

    // -------------- 辅助方法 ------------------------
    
    private ServerVO convertToVO(ServerEntity entity) {
        ServerVO vo = new ServerVO();
        vo.setServerId(entity.getServerId());
        vo.setServerType(entity.getServerType());
        vo.setServerName(entity.getServerName());
        vo.setHostname(entity.getHostname());
        vo.setOperatingSystem(entity.getOperatingSystem());
        vo.setHostStatus(entity.getHostStatus());
        vo.setPublicIp(entity.getPublicIp());
        vo.setPrivateIp(entity.getPrivateIp());
        vo.setCpuCores(entity.getCpuCores());
        vo.setMemorySize(entity.getMemorySize());
        vo.setSystemDiskSize(entity.getSystemDiskSize());
        vo.setDataDiskSize(entity.getDataDiskSize());
        vo.setUsageStatus(entity.getUsageStatus());
        vo.setLocation(entity.getLocation());
        vo.setDepartmentId(entity.getDepartmentId());
        vo.setResponsiblePerson(entity.getResponsiblePerson());
        vo.setContactInfo(entity.getContactInfo());
        vo.setVendor(entity.getVendor());
        vo.setModel(entity.getModel());
        vo.setSyncSource(entity.getSyncSource());
        vo.setExternalId(entity.getExternalId());
        vo.setLastSyncTime(entity.getLastSyncTime());
        vo.setSyncStatus(entity.getSyncStatus());
        vo.setCreateTime(entity.getCreateTime());
        vo.setUpdateTime(entity.getUpdateTime());
        return vo;
    }

}
