package cn.edu.xmut.soms.admin.module.asset.server.domain.dto.vmware;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * VMware 虚拟机 SCSI 适配器信息 DTO
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VmwareVmScsiAdapterDTO {

    /**
     * SCSI 适配器标签
     */
    private String label;

    /**
     * SCSI 适配器类型
     */
    private String type;

    /**
     * SCSI 地址信息
     */
    private Object scsi;

    /**
     * PCI 插槽编号
     */
    private Integer pciSlotNumber;

    /**
     * 共享类型
     */
    private String sharing;
}
