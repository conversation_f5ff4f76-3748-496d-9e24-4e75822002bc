package cn.edu.xmut.soms.admin.module.asset.server.constant;

import cn.edu.xmut.soms.base.common.enumeration.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * VMware 虚拟机硬件连接状态枚举
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@AllArgsConstructor
@Getter
public enum VmwareConnectionStateEnum implements BaseEnum {
    /**
     * 已连接
     */
    CONNECTED(1, "CONNECTED", "已连接"),

    /**
     * 可连接
     */
    CONNECTABLE(2, "CONNECTABLE", "可连接"),

    /**
     * 不可连接
     */
    NOT_CONNECTABLE(3, "NOT_CONNECTABLE", "不可连接"),
    ;

    private final Integer value;

    private final String code;

    private final String desc;
}
