package cn.edu.xmut.soms.admin.module.asset.server.constant;

import cn.edu.xmut.soms.base.common.enumeration.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * VMware 虚拟机网络协议枚举
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@AllArgsConstructor
@Getter
public enum VmwareNetworkProtocolEnum implements BaseEnum {
    /**
     * IPv4
     */
    IPV4(1, "IPV4", "IPv4"),

    /**
     * IPv6
     */
    IPV6(2, "IPV6", "IPv6"),
    ;

    private final Integer value;

    private final String code;

    private final String desc;
}
