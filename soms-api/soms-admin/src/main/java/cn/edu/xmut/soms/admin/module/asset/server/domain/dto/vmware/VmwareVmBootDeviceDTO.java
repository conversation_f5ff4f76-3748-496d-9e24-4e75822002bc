package cn.edu.xmut.soms.admin.module.asset.server.domain.dto.vmware;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * VMware 虚拟机启动设备 DTO
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VmwareVmBootDeviceDTO {

    /**
     * 启动设备类型
     */
    private String type;

    /**
     * 网络适配器 ID（当类型为网络启动时）
     */
    private String nic;

    /**
     * 磁盘 ID 列表（当类型为磁盘启动时）
     */
    private List<String> disks;
}
