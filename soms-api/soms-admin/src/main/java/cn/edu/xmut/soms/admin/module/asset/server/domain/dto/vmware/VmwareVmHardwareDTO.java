package cn.edu.xmut.soms.admin.module.asset.server.domain.dto.vmware;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * VMware 虚拟机硬件信息 DTO
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VmwareVmHardwareDTO {

    /**
     * 硬件版本
     */
    private String version;

    /**
     * 升级策略
     */
    private String upgradePolicy;

    /**
     * 升级版本
     */
    private String upgradeVersion;

    /**
     * 升级状态
     */
    private String upgradeStatus;

    /**
     * 升级错误信息
     */
    private Object upgradeError;
}
