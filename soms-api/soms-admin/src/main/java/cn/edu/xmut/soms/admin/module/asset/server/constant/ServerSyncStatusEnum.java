package cn.edu.xmut.soms.admin.module.asset.server.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import cn.edu.xmut.soms.base.common.enumeration.BaseEnum;

/**
 * 服务器同步状态枚举
 *
 * <AUTHOR>
 * @Date 2025-07-12
 */

@AllArgsConstructor
@Getter
public enum ServerSyncStatusEnum implements BaseEnum {

    /**
     * 进行中
     */
    PROCESSING(0, "进行中"),

    /**
     * 成功
     */
    SUCCESS(1, "成功"),

    /**
     * 失败
     */
    FAILED(2, "失败");

    private final Integer value;

    private final String desc;

}