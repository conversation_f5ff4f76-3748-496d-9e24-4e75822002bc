package cn.edu.xmut.soms.admin.module.asset.server.domain.dto.vmware;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * VMware 虚拟机软盘驱动器信息 DTO
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VmwareVmFloppyDTO {

    /**
     * 软盘驱动器标签
     */
    private String label;

    /**
     * 软盘驱动器后端配置
     */
    private VmwareVmFloppyBackingDTO backing;

    /**
     * 连接状态
     */
    private String state;

    /**
     * 启动时连接标志
     */
    private Boolean startConnected;

    /**
     * 允许客户机控制标志
     */
    private Boolean allowGuestControl;

    /**
     * 软盘驱动器后端配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VmwareVmFloppyBackingDTO {
        /**
         * 后端类型
         */
        private String type;

        /**
         * 镜像文件路径
         */
        private String imageFile;

        /**
         * 主机设备
         */
        private String hostDevice;

        /**
         * 自动检测标志
         */
        private Boolean autoDetect;
    }
}
