package cn.edu.xmut.soms.admin.module.asset.server.domain.dto.vmware;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * VMware 虚拟机 CD-ROM 信息 DTO
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VmwareVmCdromDTO {

    /**
     * CD-ROM 类型
     */
    private String type;

    /**
     * CD-ROM 标签
     */
    private String label;

    /**
     * IDE 地址信息
     */
    private Object ide;

    /**
     * SATA 地址信息
     */
    private Object sata;

    /**
     * CD-ROM 后端配置
     */
    private VmwareVmCdromBackingDTO backing;

    /**
     * 连接状态
     */
    private String state;

    /**
     * 启动时连接标志
     */
    private Boolean startConnected;

    /**
     * 允许客户机控制标志
     */
    private Boolean allowGuestControl;

    /**
     * CD-ROM 后端配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VmwareVmCdromBackingDTO {
        /**
         * 后端类型
         */
        private String type;

        /**
         * ISO 文件路径
         */
        private String isoFile;

        /**
         * 主机设备
         */
        private String hostDevice;

        /**
         * 自动检测标志
         */
        private Boolean autoDetect;

        /**
         * 设备访问类型
         */
        private String deviceAccessType;
    }
}
