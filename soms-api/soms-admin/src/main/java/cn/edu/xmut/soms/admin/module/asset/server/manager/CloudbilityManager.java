package cn.edu.xmut.soms.admin.module.asset.server.manager;

import cn.edu.xmut.soms.admin.module.asset.server.domain.dto.cloudbility.CloudHostResultDTO;
import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 *
 * 行云管家接口封装
 *
 * <AUTHOR>
 * @Date 2025/7/10 11:37
 */
@Service
@Slf4j
public class CloudbilityManager {

    private static final String BASE_URL = "https://base.xmut.edu.cn/api/openapi";

    private static final String ACCESS_KEY_ID = "N1rGMdQVTGt4nDzE";

    private static final String ACCESS_KEY_SECRET = "w4tzjdSsZyRJXb3DdtIIxEw637AvfV";

    private static final String TEAM_ID = "182826779545600";

    /**
     * Token缓存键
     */
    private static final String TOKEN_CACHE_KEY = "access_token";

    /**
     * Token过期时间，单位秒（2小时 - 5分钟缓冲时间）
     */
    private static final long TOKEN_EXPIRE_TIME = 7200 - 300;

    /**
     * TTL缓存实例，用于存储Token
     */
    private final Cache<String, String> tokenCache = CacheUtil.newTimedCache(TOKEN_EXPIRE_TIME * 1000);


    /**
     * 获取团队所有主机并解析为CloudHostResultDTO（使用默认分页参数）
     *
     * @return 解析后的CloudHostResultDTO对象
     */
    public CloudHostResultDTO getTeamHostsAsDTO() {
        return getTeamHosts(1, 20);
    }


    /**
     * 获取团队所有主机
     *
     * @param page 页码，默认为1
     * @param size 每页记录数，默认为20
     * @return 主机列表的JSON响应
     */
    public CloudHostResultDTO getTeamHosts(Integer page, Integer size) {
        try {
            String token = getAccessToken();
            if (token == null) {
                return null;
            }
            Map<String, Object> params = new HashMap<>();
            params.put("page", page);
            params.put("size", size);

            String url = BASE_URL + "/host/byTeam/" + TEAM_ID ;

            String response = HttpRequest.get(url)
                    .header("Authorization", token)
                    .form(params)
                    .execute()
                    .body();

            return JSONUtil.toBean(response, CloudHostResultDTO.class);

        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 根据云账户ID获取主机列表
     *
     * @param cloudId 云账户ID
     * @param page 页码，默认为1
     * @param size 每页记录数，默认为20
     * @return 主机列表的JSON响应
     */
    public CloudHostResultDTO getHostsByCloud(Integer cloudId, Integer page, Integer size) {
        try {
            String token = getAccessToken();
            if (token == null) {
                log.error("获取访问Token失败");
                return null;
            }
            
            Map<String, Object> params = new HashMap<>();
            params.put("page", page);
            params.put("size", size);

            String url = BASE_URL + "/host/byCloud/" + cloudId;

            String response = HttpRequest.get(url)
                    .header("Authorization", token)
                    .form(params)
                    .execute()
                    .body();

            return JSONUtil.toBean(response, CloudHostResultDTO.class);

        } catch (Exception e) {
            log.error("根据云账户ID获取主机列表失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取团队主机优化建议
     *
     * @param teamId 团队ID
     * @return 优化建议JSON响应
     */
    public String getTeamHostAdvice(String teamId) {
        try {
            String token = getAccessToken();
            if (token == null) {
                log.error("获取访问Token失败");
                return null;
            }

            String url = BASE_URL + "/host/advice/byTeam/" + teamId;

            String response = HttpRequest.get(url)
                    .header("Authorization", token)
                    .execute()
                    .body();

            return response;

        } catch (Exception e) {
            log.error("获取团队主机优化建议失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取指定主机的优化建议
     *
     * @param hostId 主机ID
     * @return 优化建议JSON响应
     */
    public String getHostAdvice(Integer hostId) {
        try {
            String token = getAccessToken();
            if (token == null) {
                log.error("获取访问Token失败");
                return null;
            }

            String url = BASE_URL + "/host/advice/" + hostId;

            String response = HttpRequest.get(url)
                    .header("Authorization", token)
                    .execute()
                    .body();

            return response;

        } catch (Exception e) {
            log.error("获取主机优化建议失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取所有团队主机（支持大批量数据获取）
     *
     * @return 所有主机列表
     */
    public CloudHostResultDTO getAllTeamHosts() {
        CloudHostResultDTO allResults = new CloudHostResultDTO();
        // 设置大的page size来获取更多数据
        return getTeamHosts(1, 100);
    }

    /**
     * 检查Token是否有效
     *
     * @return Token是否有效
     */
    public boolean isTokenValid() {
        String cachedToken = tokenCache.get(TOKEN_CACHE_KEY);
        return cachedToken != null;
    }

    /**
     * 强制刷新Token
     *
     * @return 新的Token
     */
    public String refreshToken() {
        tokenCache.remove(TOKEN_CACHE_KEY);
        return getAccessToken();
    }

    /**
     * 获取访问Token
     * 
     * @return 访问Token
     */
    public String getAccessToken() {
        // 先从缓存中获取
        String cachedToken = tokenCache.get(TOKEN_CACHE_KEY);
        if (cachedToken != null) {
            return cachedToken;
        }

        try {
            Map<String, Object> params = new HashMap<>();
            params.put("accessKeyId", ACCESS_KEY_ID);
            params.put("accessKeySecret", ACCESS_KEY_SECRET);
            params.put("expireSeconds", 7200); // 2小时过期时间
            
            String url = BASE_URL + "/oauth";
            String response = HttpUtil.get(url, params);
            
            JSONObject jsonResponse = JSONUtil.parseObj(response);
            
            if (jsonResponse.containsKey("token")) {
                String token = jsonResponse.getStr("token");
                tokenCache.put(TOKEN_CACHE_KEY, token);
                log.info("成功获取行云管家访问Token");
                return token;
            } else {
                log.error("行云管家Token响应格式异常: {}", response);
                return null;
            }
            
        } catch (Exception e) {
            log.error("获取行云管家访问Token失败: {}", e.getMessage(), e);
            return null;
        }
    }
}
