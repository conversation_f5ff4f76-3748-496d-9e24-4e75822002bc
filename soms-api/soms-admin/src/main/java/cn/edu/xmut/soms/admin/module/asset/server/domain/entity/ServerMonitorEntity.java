package cn.edu.xmut.soms.admin.module.asset.server.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 服务器监控数据表 实体类
 *
 * <AUTHOR>
 * @Date 2025-07-12
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_server_monitor")
public class ServerMonitorEntity {

    /**
     * 监控记录ID
     */
    @TableId(type = IdType.AUTO)
    private Long monitorId;

    /**
     * 服务器ID
     */
    private Long serverId;

    /**
     * CPU使用率(%)
     */
    private BigDecimal cpuUsage;

    /**
     * 内存使用率(%)
     */
    private BigDecimal memoryUsage;

    /**
     * 磁盘使用率(%)
     */
    private BigDecimal diskUsage;

    /**
     * 网络入流量(bytes)
     */
    private Long networkIn;

    /**
     * 网络出流量(bytes)
     */
    private Long networkOut;

    /**
     * 系统负载
     */
    private BigDecimal loadAverage;

    /**
     * 运行时间(秒)
     */
    private Long uptime;

    /**
     * 监控时间
     */
    private LocalDateTime monitorTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

}