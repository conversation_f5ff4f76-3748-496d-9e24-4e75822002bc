package cn.edu.xmut.soms.admin.module.asset.server.constant;

import cn.edu.xmut.soms.base.common.enumeration.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 服务器状态枚举
 *
 * <AUTHOR>
 * @date 2025/07/10
 */
@AllArgsConstructor
@Getter
public enum ServerStatusEnum implements BaseEnum {
    /**
     * 1 运行中
     */
    RUNNING(1, "运行中"),

    /**
     * 2 关机
     */
    SHUTDOWN(2, "关机"),
    ;

    private final Integer value;

    private final String desc;
}
