package cn.edu.xmut.soms.admin.module.asset.server.domain.dto.vmware;

import cn.edu.xmut.soms.admin.module.asset.server.constant.VmwareGuestOSEnum;
import cn.edu.xmut.soms.admin.module.asset.server.constant.VmwarePowerStateEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * VMware 虚拟机信息 DTO
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VmwareVmInfoDTO {

    /**
     * 虚拟机 ID
     */
    private String vmId;

    /**
     * 客户操作系统
     */
    private VmwareGuestOSEnum guestOS;

    /**
     * 虚拟机名称
     */
    private String name;

    /**
     * 虚拟机身份信息
     */
    private VmwareVmIdentityDTO identity;

    /**
     * 电源状态
     */
    private VmwarePowerStateEnum powerState;

    /**
     * 即时克隆冻结标志
     */
    private Boolean instantCloneFrozen;

    /**
     * 硬件信息
     */
    private VmwareVmHardwareDTO hardware;

    /**
     * 启动信息
     */
    private VmwareVmBootDTO boot;

    /**
     * 启动设备列表
     */
    private List<VmwareVmBootDeviceDTO> bootDevices;

    /**
     * CPU 信息
     */
    private VmwareVmCpuDTO cpu;

    /**
     * 内存信息
     */
    private VmwareVmMemoryDTO memory;

    /**
     * 磁盘信息映射
     * Key: 磁盘 ID, Value: 磁盘信息
     */
    private Map<String, VmwareVmDiskDTO> disks;

    /**
     * 网络适配器信息映射
     * Key: 网络适配器 ID, Value: 网络适配器信息
     */
    private Map<String, VmwareVmNicDTO> nics;

    /**
     * CD-ROM 信息映射
     * Key: CD-ROM ID, Value: CD-ROM 信息
     */
    private Map<String, VmwareVmCdromDTO> cdroms;

    /**
     * 软盘驱动器信息映射
     * Key: 软盘驱动器 ID, Value: 软盘驱动器信息
     */
    private Map<String, VmwareVmFloppyDTO> floppies;

    /**
     * 并行端口信息映射
     * Key: 并行端口 ID, Value: 并行端口信息
     */
    private Map<String, VmwareVmParallelPortDTO> parallelPorts;

    /**
     * 串行端口信息映射
     * Key: 串行端口 ID, Value: 串行端口信息
     */
    private Map<String, VmwareVmSerialPortDTO> serialPorts;

    /**
     * SATA 适配器信息映射
     * Key: SATA 适配器 ID, Value: SATA 适配器信息
     */
    private Map<String, VmwareVmSataAdapterDTO> sataAdapters;

    /**
     * SCSI 适配器信息映射
     * Key: SCSI 适配器 ID, Value: SCSI 适配器信息
     */
    private Map<String, VmwareVmScsiAdapterDTO> scsiAdapters;

    /**
     * NVMe 适配器信息映射
     * Key: NVMe 适配器 ID, Value: NVMe 适配器信息
     */
    private Map<String, VmwareVmNvmeAdapterDTO> nvmeAdapters;

    /**
     * 所属主机 ID
     */
    private String hostId;

    /**
     * 所属集群 ID
     */
    private String clusterId;

    /**
     * 所属数据中心 ID
     */
    private String datacenterId;

    /**
     * 所属资源池 ID
     */
    private String resourcePoolId;

    /**
     * 虚拟机目录 ID
     */
    private String folderId;

    /**
     * 自定义属性
     */
    private Map<String, Object> customAttributes;
}
