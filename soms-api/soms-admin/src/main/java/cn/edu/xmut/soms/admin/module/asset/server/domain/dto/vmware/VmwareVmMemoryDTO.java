package cn.edu.xmut.soms.admin.module.asset.server.domain.dto.vmware;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * VMware 虚拟机内存信息 DTO
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VmwareVmMemoryDTO {

    /**
     * 内存大小（MiB）
     */
    private Long sizeMiB;

    /**
     * 热添加启用标志
     */
    private Boolean hotAddEnabled;

    /**
     * 热添加增量大小（MiB）
     */
    private Long hotAddIncrementSizeMiB;

    /**
     * 热添加限制大小（MiB）
     */
    private Long hotAddLimitMiB;
}
