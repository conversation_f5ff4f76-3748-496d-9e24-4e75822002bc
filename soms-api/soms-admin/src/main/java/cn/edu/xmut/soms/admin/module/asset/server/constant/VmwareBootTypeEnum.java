package cn.edu.xmut.soms.admin.module.asset.server.constant;

import cn.edu.xmut.soms.base.common.enumeration.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * VMware 虚拟机启动类型枚举
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@AllArgsConstructor
@Getter
public enum VmwareBootTypeEnum implements BaseEnum {
    /**
     * BIOS 启动
     */
    BIOS(1, "BIOS", "BIOS 启动"),

    /**
     * EFI 启动
     */
    EFI(2, "EFI", "EFI 启动"),
    ;

    private final Integer value;

    private final String code;

    private final String desc;
}
