package cn.edu.xmut.soms.admin.constant;

import cn.edu.xmut.soms.base.constant.SwaggerTagConst;

/**
 * swagger
 *
 * <AUTHOR>
 * @Date 2022-01-07 18:59:22
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright  <a href="https://1024lab.net">1024创新实验室</a>
 */
public class AdminSwaggerTagConst extends SwaggerTagConst {

    public static class Business {
        public static final String MANAGER_CATEGORY = "ERP进销存-分类管理";

        public static final String MANAGER_GOODS = "ERP进销存-商品管理";

        public static final String OA_BANK = "OA办公-银行卡信息";

        public static final String OA_ENTERPRISE = "OA办公-企业";

        public static final String OA_INVOICE = "OA办公-发票信息";

        public static final String OA_NOTICE = "OA办公-通知公告";

    }


    public static class System {

        public static final String SYSTEM_LOGIN = "系统-员工登录";

        public static final String SYSTEM_EMPLOYEE = "系统-员工管理";

        public static final String SYSTEM_DEPARTMENT = "系统-部门管理";

        public static final String SYSTEM_MENU = "系统-菜单";

        public static final String SYSTEM_DATA_SCOPE = "系统-系统-数据范围";

        public static final String SYSTEM_ROLE = "系统-角色";

        public static final String SYSTEM_ROLE_DATA_SCOPE = "系统-角色-数据范围";

        public static final String SYSTEM_ROLE_EMPLOYEE = "系统-角色-员工";

        public static final String SYSTEM_ROLE_MENU = "系统-角色-菜单";

        public static final String SYSTEM_POSITION = "系统-职务管理";

        public static final String SYSTEM_MESSAGE = "系统-消息";

    }


}
