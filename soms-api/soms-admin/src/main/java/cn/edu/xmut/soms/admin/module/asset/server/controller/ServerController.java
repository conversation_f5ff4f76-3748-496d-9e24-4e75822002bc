package cn.edu.xmut.soms.admin.module.asset.server.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.edu.xmut.soms.admin.module.asset.server.domain.form.ServerQueryForm;
import cn.edu.xmut.soms.admin.module.asset.server.domain.vo.ServerVO;
import cn.edu.xmut.soms.admin.module.asset.server.service.ServerService;
import cn.edu.xmut.soms.base.common.domain.PageResult;
import cn.edu.xmut.soms.base.common.domain.ResponseDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 服务器管理 Controller
 *
 * <AUTHOR>
 * @Date 2025-07-10 11:13:02
 */

@RestController
@Tag(name = "服务器管理")
public class ServerController {

    @Resource
    private ServerService serverService;

    @Operation(summary = "分页查询服务器列表 <AUTHOR>
    @PostMapping("/server/page/query")
    @SaCheckPermission("server:query")
    public ResponseDTO<PageResult<ServerVO>> queryPage(@RequestBody @Valid ServerQueryForm queryForm) {
        return ResponseDTO.ok(serverService.queryPage(queryForm));
    }

    @Operation(summary = "根据ID查询服务器详情 <AUTHOR>
    @GetMapping("/server/detail/{serverId}")
    @SaCheckPermission("server:query")
    public ResponseDTO<ServerVO> queryById(@Parameter(description = "服务器ID") @PathVariable Long serverId) {
        return ResponseDTO.ok(serverService.queryById(serverId));
    }

    @Operation(summary = "手动同步服务器数据 <AUTHOR>
    @PostMapping("/server/sync/manual")
    @SaCheckPermission("server:sync")
    public ResponseDTO<Map<String, Long>> manualSync(
            @Parameter(description = "同步来源：XINGYUN-行云管家，VMWARE-VMware，ALL-全部") 
            @RequestParam(defaultValue = "ALL") String syncSource) {
        return serverService.manualSyncServer(syncSource);
    }

    @Operation(summary = "获取服务器原始数据 <AUTHOR>
    @GetMapping("/server/originalData/{serverId}")
    @SaCheckPermission("server:query")
    public ResponseDTO<Object> getOriginalData(@Parameter(description = "服务器ID") @PathVariable Long serverId) {
        return serverService.getServerOriginalData(serverId);
    }

    @Operation(summary = "虚拟机开机 <AUTHOR>
    @PostMapping("/server/vm/start/{serverId}")
    @SaCheckPermission("server:operate")
    public ResponseDTO<Boolean> startVm(@Parameter(description = "服务器ID") @PathVariable Long serverId) {
        return serverService.startVm(serverId);
    }

    @Operation(summary = "虚拟机关机 <AUTHOR>
    @PostMapping("/server/vm/stop/{serverId}")
    @SaCheckPermission("server:operate")
    public ResponseDTO<Boolean> stopVm(@Parameter(description = "服务器ID") @PathVariable Long serverId) {
        return serverService.stopVm(serverId);
    }

    @Operation(summary = "虚拟机重启 <AUTHOR>
    @PostMapping("/server/vm/restart/{serverId}")
    @SaCheckPermission("server:operate")
    public ResponseDTO<Boolean> restartVm(@Parameter(description = "服务器ID") @PathVariable Long serverId) {
        return serverService.restartVm(serverId);
    }

    @Operation(summary = "获取虚拟机电源状态 <AUTHOR>
    @GetMapping("/server/vm/powerState/{serverId}")
    @SaCheckPermission("server:query")
    public ResponseDTO<String> getVmPowerState(@Parameter(description = "服务器ID") @PathVariable Long serverId) {
        return serverService.getVmPowerState(serverId);
    }

    @Operation(summary = "批量删除服务器 <AUTHOR>
    @PostMapping("/server/batch/delete")
    @SaCheckPermission("server:delete")
    public ResponseDTO<Void> batchDelete(@RequestBody List<Long> serverIds) {
        return serverService.batchDelete(serverIds);
    }

    @Operation(summary = "获取同步状态统计 <AUTHOR>
    @GetMapping("/server/sync/statistics")
    @SaCheckPermission("server:query")
    public ResponseDTO<Map<String, Object>> getSyncStatistics() {
        return serverService.getSyncStatistics();
    }

}
