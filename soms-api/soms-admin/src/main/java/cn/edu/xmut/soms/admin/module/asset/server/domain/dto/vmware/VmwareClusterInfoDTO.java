package cn.edu.xmut.soms.admin.module.asset.server.domain.dto.vmware;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * VMware 集群信息 DTO
 *
 * <AUTHOR>
 * @Date 2025-07-11
 */
@Data
@Builder
@NoArgsConstructor
public class VmwareClusterInfoDTO {

    /**
     * 集群 ID
     */
    private String clusterId;

    /**
     * 集群名称
     */
    private String clusterName;

    /**
     * 是否启用高可用性 (HA)
     */
    private Boolean haEnabled;

    /**
     * 是否启用分布式资源调度器 (DRS)
     */
    private Boolean drsEnabled;

    /**
     * 带参构造函数
     */
    public VmwareClusterInfoDTO(String clusterId, String clusterName, Boolean haEnabled, Boolean drsEnabled) {
        this.clusterId = clusterId;
        this.clusterName = clusterName;
        this.haEnabled = haEnabled;
        this.drsEnabled = drsEnabled;
    }
}
