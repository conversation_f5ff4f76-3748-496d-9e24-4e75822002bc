package cn.edu.xmut.soms.admin.module.asset.server.domain.dto.vmware;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * VMware 虚拟机并行端口信息 DTO
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VmwareVmParallelPortDTO {

    /**
     * 并行端口标签
     */
    private String label;

    /**
     * 并行端口后端配置
     */
    private VmwareVmParallelPortBackingDTO backing;

    /**
     * 连接状态
     */
    private String state;

    /**
     * 启动时连接标志
     */
    private Boolean startConnected;

    /**
     * 允许客户机控制标志
     */
    private Boolean allowGuestControl;

    /**
     * 并行端口后端配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VmwareVmParallelPortBackingDTO {
        /**
         * 后端类型
         */
        private String type;

        /**
         * 文件路径
         */
        private String file;

        /**
         * 主机设备
         */
        private String hostDevice;

        /**
         * 自动检测标志
         */
        private Boolean autoDetect;
    }
}
