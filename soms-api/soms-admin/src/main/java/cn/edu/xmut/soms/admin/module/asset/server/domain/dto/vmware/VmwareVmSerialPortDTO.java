package cn.edu.xmut.soms.admin.module.asset.server.domain.dto.vmware;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * VMware 虚拟机串行端口信息 DTO
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VmwareVmSerialPortDTO {

    /**
     * 串行端口标签
     */
    private String label;

    /**
     * 轮询时让出标志
     */
    private Boolean yieldOnPoll;

    /**
     * 串行端口后端配置
     */
    private VmwareVmSerialPortBackingDTO backing;

    /**
     * 连接状态
     */
    private String state;

    /**
     * 启动时连接标志
     */
    private Boolean startConnected;

    /**
     * 允许客户机控制标志
     */
    private Boolean allowGuestControl;

    /**
     * 串行端口后端配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VmwareVmSerialPortBackingDTO {
        /**
         * 后端类型
         */
        private String type;

        /**
         * 文件路径
         */
        private String file;

        /**
         * 主机设备
         */
        private String hostDevice;

        /**
         * 自动检测标志
         */
        private Boolean autoDetect;

        /**
         * 管道名称
         */
        private String pipe;

        /**
         * 无接收丢失标志
         */
        private Boolean noRxLoss;

        /**
         * 网络位置
         */
        private String networkLocation;

        /**
         * 代理
         */
        private String proxy;
    }
}
