package cn.edu.xmut.soms.admin.module.system.role.manager;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.edu.xmut.soms.admin.module.system.role.dao.RoleEmployeeDao;
import cn.edu.xmut.soms.admin.module.system.role.domain.entity.RoleEmployeeEntity;
import org.springframework.stereotype.Service;

/**
 * 角色员工 manager
 *
 * <AUTHOR> 卓大
 * @Date 2022-04-08 21:53:04
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright <a href="https://1024lab.net">1024创新实验室</a>
 */
@Service
public class RoleEmployeeManager extends ServiceImpl<RoleEmployeeDao, RoleEmployeeEntity> {

}
