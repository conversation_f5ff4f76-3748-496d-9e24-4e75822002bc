package cn.edu.xmut.soms.admin.module.asset.server.constant;

import cn.edu.xmut.soms.base.common.enumeration.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 服务器类型枚举
 *
 * <AUTHOR>
 * @date 2025/07/10
 */
@AllArgsConstructor
@Getter
public enum ServerTypeEnum implements BaseEnum {
    /**
     * 1 物理机
     */
    PHYSICAL_MACHINE(1, "物理机"),

    /**
     * 2 虚拟机
     */
    VIRTUAL_MACHINE(2, "虚拟机"),
    ;

    private final Integer value;

    private final String desc;
}
