package cn.edu.xmut.soms.admin.module.asset.server.service;

import cn.edu.xmut.soms.admin.module.asset.server.constant.ServerSyncStatusEnum;
import cn.edu.xmut.soms.admin.module.asset.server.dao.ServerSyncLogDao;
import cn.edu.xmut.soms.admin.module.asset.server.domain.entity.ServerSyncLogEntity;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 服务器同步日志服务
 *
 * <AUTHOR>
 * @Date 2025-07-12
 */

@Slf4j
@Service
public class ServerSyncLogService {

    @Resource
    private ServerSyncLogDao serverSyncLogDao;

    /**
     * 获取同步日志详情
     *
     * @param logId 日志ID
     * @return 日志详情
     */
    public ServerSyncLogEntity getSyncLogDetail(Long logId) {
        return serverSyncLogDao.selectById(logId);
    }

    /**
     * 获取当前同步状态
     *
     * @return 同步状态信息
     */
    public Map<String, Object> getCurrentSyncStatus() {
        Map<String, Object> result = new HashMap<>();
        
        // 获取最近的同步记录
        LambdaQueryWrapper<ServerSyncLogEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(ServerSyncLogEntity::getCreateTime)
                   .last("LIMIT 10");
        
        List<ServerSyncLogEntity> recentLogs = serverSyncLogDao.selectList(queryWrapper);
        result.put("recentLogs", recentLogs);
        
        // 获取正在进行的同步任务
        LambdaQueryWrapper<ServerSyncLogEntity> processingWrapper = new LambdaQueryWrapper<>();
        processingWrapper.eq(ServerSyncLogEntity::getSyncStatus, ServerSyncStatusEnum.PROCESSING.getValue());
        
        List<ServerSyncLogEntity> processingSyncs = serverSyncLogDao.selectList(processingWrapper);
        result.put("processingSyncs", processingSyncs);
        
        // 统计信息
        Map<String, Long> statistics = new HashMap<>();
        statistics.put("totalSyncs", (long) recentLogs.size());
        statistics.put("processingSyncs", (long) processingSyncs.size());
        
        long successCount = recentLogs.stream()
                .mapToLong(log -> ServerSyncStatusEnum.SUCCESS.getValue().equals(log.getSyncStatus()) ? 1 : 0)
                .sum();
        statistics.put("successCount", successCount);
        
        long failedCount = recentLogs.stream()
                .mapToLong(log -> ServerSyncStatusEnum.FAILED.getValue().equals(log.getSyncStatus()) ? 1 : 0)
                .sum();
        statistics.put("failedCount", failedCount);
        
        result.put("statistics", statistics);
        
        return result;
    }

}