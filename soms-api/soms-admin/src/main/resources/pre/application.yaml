#############################################################################################################
#                                                                                                           #
# 为了减少重复配置，本配置文件为此soms-admin的独有配置，更多配置请查看 sa-base 项目中的 sa-base.yaml 通用配置文件。      #
# 其中此文件中配置可以覆盖 sa-base.yaml 中的通用配置，具体实现类请看类：YamlProcessor.java                          #
#                                                                                                           #
#############################################################################################################

# 项目配置: 名称、日志目录
project:
  name: soms-admin
  log-directory: /home/<USER>/soms/${project.name}/${spring.profiles.active}

# 项目端口和url根路径
server:
  port: 1024
  servlet:
    context-path: /

# 环境
spring:
  profiles:
    active: '@profiles.active@'
