<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.edu.xmut.soms.admin.module.system.role.dao.RoleEmployeeDao">


    <resultMap id="EmployeeVO"
               type="cn.edu.xmut.soms.admin.module.system.employee.domain.vo.EmployeeVO"></resultMap>


    <select id="selectRoleByEmployeeId" resultType="cn.edu.xmut.soms.admin.module.system.role.domain.vo.RoleVO">
        SELECT t_role.*
        FROM t_role_employee
        left join t_role on t_role_employee.role_id = t_role.role_id
        WHERE t_role_employee.employee_id = #{employeeId}
    </select>

    <select id="selectRoleIdByEmployeeId" resultType="java.lang.Long">
        SELECT er.role_id
        FROM t_role_employee er
        WHERE er.employee_id = #{employeeId}
    </select>


    <select id="selectRoleEmployeeByName" resultMap="EmployeeVO">
        SELECT
        t_employee.employee_id,
        t_employee.login_name,
        t_employee.login_pwd,
        t_employee.actual_name,
        t_employee.phone,
        t_employee.department_id,
        t_employee.disabled_flag,
        t_employee.remark,
        t_employee.update_time,
        t_employee.create_time
        FROM
        t_role_employee
        left join t_employee  on t_role_employee.employee_id = t_employee.employee_id
        left join t_role  on t_role_employee.role_id = t_role.role_id
        <where>
            <if test="queryForm.keywords != null and queryForm.keywords!= ''">
                AND (
                INSTR(t_employee.actual_name,#{queryForm.keywords})
                OR INSTR(t_employee.phone,#{queryForm.keywords})
                OR INSTR(t_employee.login_name,#{queryForm.keywords})
                )
            </if>
            <if test="queryForm.roleId != null">
                AND t_role_employee.role_id = #{queryForm.roleId}
            </if>
        </where>
    </select>

    <select id="selectEmployeeByRoleId" resultMap="EmployeeVO">
        SELECT t_employee.employee_id,
               t_employee.login_name,
               t_employee.login_pwd,
               t_employee.actual_name,
               t_employee.phone,
               t_employee.department_id,
               t_employee.disabled_flag,
               t_employee.remark,
               t_employee.update_time,
               t_employee.create_time
        FROM t_role_employee
        left join t_employee  on t_role_employee.employee_id = t_employee.employee_id
        WHERE t_role_employee.role_id = #{roleId}
    </select>

    <select id="selectEmployeeIdByRoleIdList" resultType="java.lang.Long">
        SELECT
        employee_id
        FROM
        t_role_employee
        WHERE
        role_id in
        <foreach collection="roleIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="selectRoleIdByEmployeeIdList"
            resultType="cn.edu.xmut.soms.admin.module.system.role.domain.entity.RoleEmployeeEntity">
        SELECT
        *
        FROM
        t_role_employee
        WHERE
        employee_id in
        <foreach collection="employeeIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectRoleByEmployeeIdList"
            resultType="cn.edu.xmut.soms.admin.module.system.role.domain.vo.RoleEmployeeVO">
        SELECT
        t_role_employee.role_id,
        t_role_employee.employee_id,
        t_role.role_name
        FROM
        t_role_employee
        LEFT JOIN t_role  ON t_role.role_id = t_role_employee.role_id
        WHERE
        employee_id in
        <foreach collection="employeeIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>




    <delete id="deleteByEmployeeId">
        DELETE
        FROM t_role_employee
        WHERE employee_id = #{employeeId}
    </delete>


    <delete id="deleteByRoleId">
        DELETE
        FROM t_role_employee
        WHERE role_id = #{roleId}
    </delete>

    <delete id="deleteByEmployeeIdRoleId">
        DELETE
        FROM t_role_employee
        WHERE role_id = #{roleId}
          and employee_id = #{employeeId}
    </delete>


    <delete id="batchDeleteEmployeeRole">
        DELETE FROM t_role_employee
        WHERE role_id = #{roleId} and employee_id in
        <foreach collection="employeeIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>

    <select id="existsByRoleId" resultType="java.lang.Integer">
        SELECT 1
        FROM t_role_employee er
        WHERE er.role_id = #{roleId}
        LIMIT 1
    </select>

</mapper>