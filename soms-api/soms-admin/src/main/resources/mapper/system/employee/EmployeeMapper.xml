<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.edu.xmut.soms.admin.module.system.employee.dao.EmployeeDao">

    <select id="queryEmployee" resultType="cn.edu.xmut.soms.admin.module.system.employee.domain.vo.EmployeeVO">
        SELECT
        t_employee.*,
        t_department.department_name
        FROM t_employee
        LEFT JOIN t_department ON t_department.department_id = t_employee.department_id
        <where>
            <if test="queryForm.keyword != null and queryForm.keyword != ''">
                AND (
                INSTR(t_employee.actual_name,#{queryForm.keyword})
                OR INSTR(t_employee.phone,#{queryForm.keyword})
                OR INSTR(t_employee.login_name,#{queryForm.keyword})
                )
            </if>
            <if test="departmentIdList != null and departmentIdList.size > 0">
                and
                t_employee.department_id
                in
                <foreach collection="departmentIdList" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="queryForm.disabledFlag != null">
                AND t_employee.disabled_flag = #{queryForm.disabledFlag}
            </if>
            <if test="queryForm.deletedFlag != null">
                AND t_employee.deleted_flag = #{queryForm.deletedFlag}
            </if>
        </where>
    </select>

    <update id="updateDisableFlag">
        UPDATE t_employee
        SET disabled_flag = #{disabledFlag}
        WHERE employee_id = #{employeeId}
    </update>

    <select id="getByLoginName" resultType="cn.edu.xmut.soms.admin.module.system.employee.domain.entity.EmployeeEntity">
        SELECT *
        FROM t_employee
        <where>
            login_name = #{loginName}
            <if test="deletedFlag != null">
                AND deleted_flag = #{deletedFlag}
            </if>
        </where>
    </select>

    <select id="getByActualName" resultType="cn.edu.xmut.soms.admin.module.system.employee.domain.entity.EmployeeEntity">
        SELECT *
        FROM t_employee
        <where>
            actual_name = #{actualName}
            <if test="deletedFlag != null">
                AND deleted_flag = #{deletedFlag}
            </if>
        </where>
    </select>

    <select id="getByPhone" resultType="cn.edu.xmut.soms.admin.module.system.employee.domain.entity.EmployeeEntity">
        SELECT *
        FROM t_employee
        <where>
            phone = #{phone}
            <if test="deletedFlag != null">
                AND deleted_flag = #{deletedFlag}
            </if>
        </where>
    </select>

    <select id="getByEmail" resultType="cn.edu.xmut.soms.admin.module.system.employee.domain.entity.EmployeeEntity">
        SELECT *
        FROM t_employee
        <where>
            email = #{email}
            <if test="deletedFlag != null">
                AND deleted_flag = #{deletedFlag}
            </if>
        </where>
    </select>

    <select id="listAll" resultType="cn.edu.xmut.soms.admin.module.system.employee.domain.vo.EmployeeVO">
        SELECT *
        FROM t_employee
        ORDER BY create_time DESC
    </select>

    <select id="countByDepartmentId" resultType="integer">
        SELECT count(1) FROM t_employee
        WHERE
        department_id = #{departmentId} AND deleted_flag = #{deletedFlag}
    </select>

    <select id="selectByDepartmentId" resultType="cn.edu.xmut.soms.admin.module.system.employee.domain.entity.EmployeeEntity">
        SELECT *
        FROM t_employee
        <where>
            department_id = #{departmentId}
            <if test="deletedFlag != null">
                AND deleted_flag = #{deletedFlag}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectByActualName" resultType="cn.edu.xmut.soms.admin.module.system.employee.domain.entity.EmployeeEntity">
        SELECT * FROM t_employee
        <where>
            actual_name = #{actualName}
            AND department_id in
            <foreach collection="departmentIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            <if test="deletedFlag != null">
                AND deleted_flag = #{deletedFlag}
            </if>
        </where>
    </select>

    <select id="getEmployeeIdByDepartmentIdList" resultType="java.lang.Long">
        SELECT employee_id
        FROM t_employee
        <where>
            department_id IN
            <foreach collection="departmentIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            <if test="deletedFlag != null">
                AND deleted_flag = #{deletedFlag}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="getEmployeeId" resultType="java.lang.Long">
        SELECT employee_id
        FROM t_employee
        <where>
            <if test="deletedFlag != null">
                AND deleted_flag = #{deletedFlag}
            </if>
        </where>
    </select>

    <select id="getEmployeeIdByDepartmentId" resultType="java.lang.Long">
        SELECT employee_id
        FROM t_employee
        <where>
            department_id = #{departmentId}
            <if test="deletedFlag != null">
                AND deleted_flag = #{deletedFlag}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="getEmployeeByIds" resultType="cn.edu.xmut.soms.admin.module.system.employee.domain.vo.EmployeeVO">
        SELECT * FROM t_employee
        where employee_id IN
        <foreach collection="employeeIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        ORDER BY create_time DESC
    </select>

    <select id="getEmployeeById" resultType="cn.edu.xmut.soms.admin.module.system.employee.domain.vo.EmployeeVO">
        SELECT t_employee.*,
               t_department.department_name
        FROM t_employee
                 LEFT JOIN t_department ON t_department.department_id = t_employee.department_id
        where t_employee.employee_id = #{employeeId}
    </select>

    <select id="selectEmployeeByDisabledAndDeleted" resultType="cn.edu.xmut.soms.admin.module.system.employee.domain.vo.EmployeeVO">
        SELECT
        t_employee.*,
        t_department.department_name
        FROM t_employee
        LEFT JOIN t_department ON t_department.department_id = t_employee.department_id
        <where>
            <if test="disabledFlag != null">
                AND t_employee.disabled_flag = #{disabledFlag}
            </if>
            <if test="deletedFlag != null">
                AND t_employee.deleted_flag = #{deletedFlag}
            </if>
        </where>
    </select>

    <update id="updatePassword">
        UPDATE t_employee
        SET login_pwd = #{password}
        WHERE employee_id = #{employeeId}
    </update>

</mapper>