<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.edu.xmut.soms.admin.module.system.menu.dao.MenuDao">

    <delete id="deleteByMenuIdList">
        <if test="menuIdList != null and menuIdList.size > 0">
            <foreach collection="menuIdList" item="item" separator=";">
                update t_menu
                set deleted_flag = #{deletedFlag},
                update_user_id = #{updateUserId}
                where menu_id = #{item}
            </foreach>
        </if>
    </delete>

    <select id="getByMenuName"
            resultType="cn.edu.xmut.soms.admin.module.system.menu.domain.entity.MenuEntity">
        select *
        from t_menu
        where menu_name = #{menuName}
          and parent_id = #{parentId}
          and deleted_flag = #{deletedFlag} limit 1
    </select>
    <select id="queryMenuList" resultType="cn.edu.xmut.soms.admin.module.system.menu.domain.vo.MenuVO">
        select * from t_menu
        where deleted_flag = #{deletedFlag}
        <if test="disabledFlag != null">
            and disabled_flag = #{disabledFlag}
        </if>
        <if test="menuTypeList != null and menuTypeList.size > 0">
            and menu_type in
            <foreach collection="menuTypeList" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        order by parent_id desc,sort asc
    </select>
    <select id="getPointListByMenuId"
            resultType="cn.edu.xmut.soms.admin.module.system.menu.domain.entity.MenuEntity">
        select *
        from t_menu
        where parent_id = #{menuId}
          and deleted_flag = #{deletedFlag}
          and menu_type = #{menuType}
    </select>

    <select id="queryMenuByEmployeeId"
            resultType="cn.edu.xmut.soms.admin.module.system.menu.domain.vo.MenuVO">
        select t_menu.* from t_menu
        left join t_role_menu on t_role_menu.menu_id = t_menu.menu_id
        left join t_role_employee on t_role_employee.role_id = t_role_menu.role_id
        where t_menu.disabled_flag = #{disabledFlag} and t_menu.deleted_flag = #{deletedFlag}
        <if test="employeeId != null">
            and t_role_employee.employee_id = #{employeeId}
        </if>
    </select>
    <select id="queryMenuByType" resultType="cn.edu.xmut.soms.admin.module.system.menu.domain.entity.MenuEntity">
        select *
        from t_menu
        where disabled_flag = #{disabledFlag}
          and deleted_flag = #{deletedFlag}
          and menu_type = #{menuType}
    </select>
    <select id="getByWebPerms" resultType="cn.edu.xmut.soms.admin.module.system.menu.domain.entity.MenuEntity">
        select *
        from t_menu
        where web_perms = #{webPerms}
          and deleted_flag = #{deletedFlag} limit 1
    </select>
    <select id="selectMenuIdByParentIdList" resultType="java.lang.Long">
        <if test="menuIdList != null and menuIdList.size > 0">
            select menu_id from t_menu where parent_id in
            <foreach collection="menuIdList" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>