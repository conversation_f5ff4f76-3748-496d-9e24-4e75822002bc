<!--
  * ${basic.description}
  *
  * @Author:    ${basic.frontAuthor}
  * @Date:      ${basic.frontDate}
  * @Copyright  ${basic.copyright}
-->
<template>
  <a-$!{insertAndUpdate.pageType}
      :title="form.$!{primaryKeyFieldName} ? '编辑' : '添加'"
      :width="$!{insertAndUpdate.width}"
      :open="visibleFlag"
      #if($!{insertAndUpdate.pageType} == 'drawer')
      @close="onClose"
      #else
      @cancel="onClose"
      #end
      :maskClosable="false"
      :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }" >
#if($insertAndUpdate.countPerLine == 1)
  #foreach ($field in $formFields)
      #if($field.frontComponent == "Input")
        <a-form-item label="$!{field.label}"  name="${field.fieldName}">
          <a-input style="width: 100%" v-model:value="form.${field.fieldName}" placeholder="$!{field.label}" />
        </a-form-item>
      #end
      #if($field.frontComponent == "InputNumber")
        <a-form-item label="$!{field.label}"  name="${field.fieldName}">
          <a-input-number style="width: 100%" v-model:value="form.${field.fieldName}" placeholder="$!{field.label}" />
        </a-form-item>
      #end
      #if($field.frontComponent == "Textarea")
        <a-form-item label="$!{field.label}"  name="${field.fieldName}">
          <a-textarea style="width: 100%" v-model:value="form.${field.fieldName}" placeholder="$!{field.label}" />
        </a-form-item>
      #end
      #if($field.frontComponent == "BooleanSelect")
        <a-form-item label="$!{field.label}"  name="${field.fieldName}">
          <BooleanSelect v-model:value="form.${field.fieldName}" style="width: 100%" />
        </a-form-item>
      #end
      #if($field.frontComponent == "SmartEnumSelect")
        <a-form-item label="$codeGeneratorTool.removeEnumDesc($!{field.label})"  name="${field.fieldName}">
          <SmartEnumSelect width="100%" v-model:value="form.${field.fieldName}" enum-name="$!{field.upperUnderscoreEnum}" placeholder="$codeGeneratorTool.removeEnumDesc($!{field.label})"/>
        </a-form-item>
      #end
      #if($field.frontComponent == "DictSelect")
        <a-form-item label="$codeGeneratorTool.removeEnumDesc($!{field.label})"  name="${field.fieldName}">
          <DictSelect width="100%" v-model:value="form.${field.fieldName}" dict-code="DICT_CODE_ENUM.$!{field.dict} || '$!{field.dict}'" placeholder="$!{field.label}"/>
        </a-form-item>
      #end
      #if($field.frontComponent == "Date")
        <a-form-item label="$!{field.label}"  name="${field.fieldName}">
          <a-date-picker valueFormat="YYYY-MM-DD" v-model:value="form.$!{field.fieldName}" style="width: 100%" placeholder="$!{field.label}"/>
        </a-form-item>
      #end
      #if($field.frontComponent == "DateTime")
        <a-form-item label="$!{field.label}"  name="${field.fieldName}">
          <a-date-picker show-time valueFormat="YYYY-MM-DD HH:mm:ss" v-model:value="form.$!{field.fieldName}" style="width: 100%" placeholder="$!{field.label}" />
        </a-form-item>
      #end
      #if($field.frontComponent == "FileUpload")
        <a-form-item label="$!{field.label}"  name="${field.fieldName}">
          <FileUpload
              :defaultFileList="form.$!{field.fieldName}"
              :folder="FILE_FOLDER_TYPE_ENUM.COMMON.value"
              buttonText="上传 $!{field.label}"
              listType="text"
              @change="e => form.$!{field.fieldName} = e"
          />
        </a-form-item>
      #end
  #end
#end
    #if($insertAndUpdate.countPerLine > 1)
      <a-row>
          #set($span=24 / $!insertAndUpdate.countPerLine )
      #foreach ($field in $formFields)
        <a-col :span="$!{span}">
        #if($field.frontComponent == "Input")
          <a-form-item label="$!{field.label}"  name="${field.fieldName}">
            <a-input style="width: 100%" v-model:value="form.${field.fieldName}" placeholder="$!{field.label}" />
          </a-form-item>
        #end
        #if($field.frontComponent == "InputNumber")
          <a-form-item label="$!{field.label}"  name="${field.fieldName}">
            <a-input-number style="width: 100%" v-model:value="form.${field.fieldName}" placeholder="$!{field.label}" />
          </a-form-item>
        #end
        #if($field.frontComponent == "Textarea")
          <a-form-item label="$!{field.label}"  name="${field.fieldName}">
            <a-textarea style="width: 100%" v-model:value="form.${field.fieldName}" placeholder="$!{field.label}" />
          </a-form-item>
        #end
        #if($field.frontComponent == "BooleanSelect")
          <a-form-item label="$!{field.label}"  name="${field.fieldName}">
            <BooleanSelect v-model:value="form.${field.fieldName}" style="width: 100%" />
          </a-form-item>
        #end
        #if($field.frontComponent == "SmartEnumSelect")
          <a-form-item label="$codeGeneratorTool.removeEnumDesc($!{field.label})"  name="${field.fieldName}">
            <SmartEnumSelect width="100%" v-model:value="form.${field.fieldName}" enum-name="$!{field.upperUnderscoreEnum}" placeholder="$codeGeneratorTool.removeEnumDesc($!{field.label})"/>
          </a-form-item>
        #end
        #if($field.frontComponent == "DictSelect")
          <a-form-item label="$codeGeneratorTool.removeEnumDesc($!{field.label})"  name="${field.fieldName}">
            <DictSelect width="100%" v-model:value="form.${field.fieldName}" dict-code="DICT_CODE_ENUM.$!{field.dict} || '$!{field.dict}'" placeholder="$!{field.label}"/>
          </a-form-item>
        #end
        #if($field.frontComponent == "Date")
          <a-form-item label="$!{field.label}"  name="${field.fieldName}">
            <a-date-picker valueFormat="YYYY-MM-DD" v-model:value="form.$!{field.fieldName}" style="width: 100%" placeholder="$!{field.label}"/>
          </a-form-item>
        #end
        #if($field.frontComponent == "DateTime")
          <a-form-item label="$!{field.label}"  name="${field.fieldName}">
            <a-date-picker show-time valueFormat="YYYY-MM-DD HH:mm:ss" v-model:value="form.$!{field.fieldName}" style="width: 100%" placeholder="$!{field.label}" />
          </a-form-item>
        #end
        #if($field.frontComponent == "FileUpload")
          <a-form-item label="$!{field.label}"  name="${field.fieldName}">
            <FileUpload
                :defaultFileList="form.$!{field.fieldName}"
                :folder="FILE_FOLDER_TYPE_ENUM.COMMON.value"
                buttonText="上传 $!{field.label}"
                listType="text"
                @change="e => form.$!{field.fieldName} = e"
            />
          </a-form-item>
        #end
        </a-col>
      #end
      </a-row>
    #end
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-$!{insertAndUpdate.pageType}>
</template>
<script setup lang="ts">
  import { reactive, ref, nextTick } from 'vue';
  import _ from 'lodash';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { $!{name.lowerCamel}Api } from '/@/api/business/$!{name.lowerHyphenCamel}/$!{name.lowerHyphenCamel}-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  #foreach ($import in $frontImportList)
  $!{import}
  #end

  // ------------------------ 事件 ------------------------

  const emits = defineEmits(['reloadList']);

  // ------------------------ 显示与隐藏 ------------------------
  // 是否显示
  const visibleFlag = ref(false);

  function show(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
    // 使用字典时把下面这注释修改成自己的字典字段 有多个字典字段就复制多份同理修改 不然打开表单时不显示字典初始值
    // if (form.status && form.status.length > 0) {
    //   form.status = form.status.map((e) => e.valueCode);
    // }
    visibleFlag.value = true;
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  function onClose() {
    Object.assign(form, formDefault);
    visibleFlag.value = false;
  }

  // ------------------------ 表单 ------------------------

  // 组件ref
  const formRef = ref();

  const formDefault = {
      #foreach ($field in $formFields)
      $!{field.fieldName}: undefined, //$!{field.label}
      #end
  };

  let form = reactive({ ...formDefault });

  const rules = {
      #foreach ($field in $formFields)
      #if($field.requiredFlag)
      $!{field.fieldName}: [{ required: true, message: '$!{field.label} 必填' }],
      #end
      #end
  };

  // 点击确定，验证表单
  async function onSubmit() {
    try {
      await formRef.value.validateFields();
      save();
    } catch (err) {
      message.error('参数验证错误，请仔细填写表单数据!');
    }
  }

  // 新建、编辑API
  async function save() {
    SmartLoading.show();
    try {
      if (form.$!{primaryKeyFieldName}) {
        await $!{name.lowerCamel}Api.update(form);
      } else {
        await $!{name.lowerCamel}Api.add(form);
      }
      message.success('操作成功');
      emits('reloadList');
      onClose();
    } catch (err) {
      smartSentry.captureError(err);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({
    show,
  });
</script>
