package cn.edu.xmut.soms.base.module.support.datatracer.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import cn.edu.xmut.soms.base.common.domain.PageParam;
import cn.edu.xmut.soms.base.common.swagger.SchemaEnum;
import cn.edu.xmut.soms.base.module.support.datatracer.constant.DataTracerTypeEnum;

/**
 * 查询表单
 *
 * <AUTHOR> 卓大
 * @Date 2022-07-23 19:38:52
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright  <a href="https://1024lab.net">1024创新实验室</a>
 */
@Data
public class DataTracerQueryForm extends PageParam {

    @SchemaEnum(DataTracerTypeEnum.class)
    private Integer type;

    @Schema(description = "业务id")
    @NotNull(message = "业务id不能为空")
    private Long dataId;

    @Schema(description = "关键字")
    private String keywords;
}
