package cn.edu.xmut.soms.base.module.support.mail.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import cn.edu.xmut.soms.base.common.enumeration.BaseEnum;

/**
 * 邮件模板类型
 *
 * <AUTHOR>
 * @Date 2024/8/5
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright <a href="https://1024lab.net">1024创新实验室</a> ，Since 2012
 */

@Getter
@AllArgsConstructor
public enum MailTemplateTypeEnum implements BaseEnum {

    STRING("string", "字符串替代器"),

    FREEMARKER("freemarker", "freemarker模板引擎");

    private String value;

    private String desc;


}