package cn.edu.xmut.soms.base.module.support.job.api.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 定时任务-更新-开启状态
 *
 * <AUTHOR>
 * @date 2024/6/17 21:30
 */
@Data
public class SmartJobEnabledUpdateForm {

    @Schema(description = "任务id")
    @NotNull(message = "任务id不能为空")
    private Integer jobId;

    @Schema(description = "是否启用")
    @NotNull(message = "是否启用不能为空")
    private Boolean enabledFlag;

    @Schema(hidden = true)
    private String updateName;
}
