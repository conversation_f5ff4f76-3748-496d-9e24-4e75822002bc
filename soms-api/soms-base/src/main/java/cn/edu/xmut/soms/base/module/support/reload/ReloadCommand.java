package cn.edu.xmut.soms.base.module.support.reload;

import jakarta.annotation.Resource;
import cn.edu.xmut.soms.base.common.util.SmartBeanUtil;
import cn.edu.xmut.soms.base.module.support.reload.core.AbstractSmartReloadCommand;
import cn.edu.xmut.soms.base.module.support.reload.core.domain.SmartReloadItem;
import cn.edu.xmut.soms.base.module.support.reload.core.domain.SmartReloadResult;
import cn.edu.xmut.soms.base.module.support.reload.dao.ReloadItemDao;
import cn.edu.xmut.soms.base.module.support.reload.dao.ReloadResultDao;
import cn.edu.xmut.soms.base.module.support.reload.domain.ReloadItemEntity;
import cn.edu.xmut.soms.base.module.support.reload.domain.ReloadResultEntity;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * reload 操作
 *
 * <AUTHOR> 卓大
 * @Date 2015-03-02 19:11:52
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright  <a href="https://1024lab.net">1024创新实验室</a>
 */
@Component
public class ReloadCommand extends AbstractSmartReloadCommand {

    @Resource
    private ReloadItemDao reloadItemDao;

    @Resource
    private ReloadResultDao reloadResultDao;

    /**
     * 读取数据库中SmartReload项
     *
     * @return List<ReloadItem>
     */
    @Override
    public List<SmartReloadItem> readReloadItem() {
        List<ReloadItemEntity> reloadItemEntityList = reloadItemDao.selectList(null);
        return SmartBeanUtil.copyList(reloadItemEntityList, SmartReloadItem.class);
    }


    /**
     * 保存reload结果
     *
     * @param smartReloadResult
     */
    @Override
    public void handleReloadResult(SmartReloadResult smartReloadResult) {
        ReloadResultEntity reloadResultEntity = SmartBeanUtil.copy(smartReloadResult, ReloadResultEntity.class);
        reloadResultDao.insert(reloadResultEntity);
    }
}
