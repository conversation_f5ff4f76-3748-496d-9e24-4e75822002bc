package cn.edu.xmut.soms.base.module.support.reload.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.edu.xmut.soms.base.module.support.reload.domain.ReloadItemEntity;
import cn.edu.xmut.soms.base.module.support.reload.domain.ReloadItemVO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * t_reload_item 数据表dao
 *
 * <AUTHOR> 卓大
 * @Date 2015-03-02 19:11:52
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright  <a href="https://1024lab.net">1024创新实验室</a>
 */
@Mapper
public interface ReloadItemDao extends BaseMapper<ReloadItemEntity> {

    List<ReloadItemVO> query();
}
