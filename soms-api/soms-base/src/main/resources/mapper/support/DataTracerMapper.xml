<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.edu.xmut.soms.base.module.support.datatracer.dao.DataTracerDao">

    <select id="selectRecord"
            resultType="cn.edu.xmut.soms.base.module.support.datatracer.domain.vo.DataTracerVO">
        select *
        from t_data_tracer
        where data_type = #{dataType}
          and data_id = #{dataId}
    </select>

    <select id="query" resultType="cn.edu.xmut.soms.base.module.support.datatracer.domain.vo.DataTracerVO">
        SELECT * FROM t_data_tracer
        <where>
            <if test="query.type != null">
                AND type = #{query.type}
            </if>
            <if test="query.dataId != null">
                AND data_id = #{query.dataId}
            </if>
            <if test="query.keywords != null and query.keywords != ''">
                AND INSTR(content,#{query.keywords})
            </if>
        </where>
        <if test="query.sortItemList == null or query.sortItemList.size == 0">
            ORDER BY data_tracer_id DESC
        </if>
    </select>

</mapper>