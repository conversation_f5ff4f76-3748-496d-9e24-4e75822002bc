<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.edu.xmut.soms.base.module.support.dict.dao.DictDao">

    <!-- 查询结果列 -->
    <sql id="base_columns">
        t_dict.dict_id,
        t_dict.dict_name,
        t_dict.dict_code,
        t_dict.remark,
        t_dict.disabled_flag,
        t_dict.create_time,
        t_dict.update_time
    </sql>

    <!-- 分页查询 -->
    <select id="queryPage" resultType="cn.edu.xmut.soms.base.module.support.dict.domain.vo.DictVO">
        SELECT
        <include refid="base_columns"/>
        FROM t_dict
        <where>
            <!--关键字-->
            <if test="queryForm.keywords != null and queryForm.keywords != ''">
                AND (
                INSTR(t_dict.dict_name,#{queryForm.keywords})
                OR INSTR(t_dict.dict_code,#{queryForm.keywords})
                OR INSTR(t_dict.remark,#{queryForm.keywords})
                )
            </if>
            <!--禁用状态-->
            <if test="queryForm.disabledFlag != null">
                AND t_dict.disabled_flag = #{queryForm.disabledFlag}
            </if>
        </where>
        <if test="queryForm.sortItemList == null or queryForm.sortItemList.size == 0">
            order by create_time desc
        </if>
    </select>

    <select id="selectByCode" resultType="cn.edu.xmut.soms.base.module.support.dict.domain.entity.DictEntity">
        select * from t_dict where dict_code = #{code}
    </select>


</mapper>
