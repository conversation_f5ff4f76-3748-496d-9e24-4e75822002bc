# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目架构

SOMS (Server Operation and Management System) 是一个基于 Spring Boot 3 + Vue 3 + Ant Design Vue 的服务器运维管理系统。采用前后端分离架构。

### 后端架构 (soms-api)

- **框架**: Spring Boot 3.3.1 + Java 17
- **数据库**: MyBatis Plus 3.5.7 + 支持多数据源
- **安全认证**: Sa-Token 1.41.0 (替代 Spring Security)
- **缓存**: Redis + Redisson 3.25.0
- **API 文档**: Knife4j 4.4.0 (基于 OpenAPI 3)
- **数据库连接池**: Druid 1.2.23

项目结构：

- `soms-base`: 基础模块，包含通用配置、工具类、异常处理等
- `soms-admin`: 管理后台模块，包含业务逻辑和 API 接口

### 前端架构 (soms-web-antdv)

- **框架**: Vue 3.4.27 + Vue Router 4 + Pinia 2.1.7
- **UI 组件**: Ant Design Vue 4.2.5
- **构建工具**: Vite 5.2.12
- **状态管理**: Pinia (替代 Vuex)
- **图表**: ECharts 5.4.3
- **编辑器**: WangEditor 5.6.34

## 开发命令

### 后端开发 (Maven)

```bash
# 编译项目 (在 soms-api 目录下)
mvn clean compile

# 运行开发环境 (默认profile为dev)
mvn spring-boot:run

# 运行指定环境
mvn spring-boot:run -Ptest    # 测试环境
mvn spring-boot:run -Ppre     # 预发布环境
mvn spring-boot:run -Pprod    # 生产环境

# 运行测试
mvn test

# 运行指定测试类
mvn test -Dtest=AdminApplicationTest

# 打包项目
mvn clean package

# 跳过测试打包
mvn clean package -DskipTests
```

### 前端开发 (在 soms-web-antdv 目录下)

```bash
# 安装依赖
npm install
# 或使用 pnpm
pnpm install

# 本地开发服务器
npm run dev

# 本地开发 (localhost模式)
npm run localhost

# 构建不同环境
npm run build:test    # 测试环境构建
npm run build:pre     # 预发布环境构建
npm run build:prod    # 生产环境构建
```

### 代码质量检查 (前端)

```bash
# ESLint 检查
npx eslint src/

# Stylelint 检查
npx stylelint "src/**/*.{vue,less,css}"

# Prettier 格式化
npx prettier --write "src/**/*.{js,vue,ts,json}"
```

## 环境配置

### 后端环境配置

配置文件位于 `src/main/resources/{env}/` 目录：

- `dev/`: 开发环境配置
- `test/`: 测试环境配置
- `pre/`: 预发布环境配置
- `prod/`: 生产环境配置

### 前端环境配置

- 开发环境: `npm run dev` (使用 vite 默认配置)
- 本地环境: `npm run localhost` (使用 localhost 模式)

## 核心特性

### 权限系统

- 基于 Sa-Token 的权限认证
- 动态路由加载：根据用户权限动态构建前端路由
- 权限指令：`v-privilege` 用于控制页面元素显示
- 数据权限：支持基于部门、角色的数据权限控制

### 代码生成

- 位于 `soms-base/src/main/resources/code-generator-template/`
- 支持 Java 后端代码生成 (Controller、Service、DAO、Entity 等)
- 支持前端代码生成 (Vue 组件、API、常量等)
- 使用 Velocity 模板引擎

### 系统监控

- 心跳检测：`HeartBeatConfig`
- 操作日志：基于 AOP 的操作日志记录
- 登录日志：记录用户登录行为
- 系统配置：动态配置管理

### 前端架构特点

- **动态路由**: 登录后从后端获取菜单权限，动态构建路由 (`main.js` 中的 `getLoginInfo()`)
- **组件库**: 基于 Ant Design Vue 的企业级组件
- **状态管理**: 使用 Pinia 进行状态管理，模块化存储
- **国际化**: 支持中英文切换 (i18n)
- **主题定制**: Less 变量定制主题
- **权限控制**: `v-privilege` 指令控制元素显示权限

## 关键目录说明

### 后端关键目录

- `soms-base/common/`: 通用工具类、常量、异常处理
- `soms-base/config/`: 系统配置类 (数据库、Redis、缓存等)
- `soms-admin/module/`: 业务模块 (asset 资产管理、system 系统管理)
- `resources/mapper/`: MyBatis XML 映射文件

### 前端关键目录

- `src/api/`: API 接口定义
- `src/components/`: 通用组件库
- `src/views/`: 页面组件
- `src/store/modules/`: Pinia 状态管理模块
- `src/router/`: 路由配置
- `src/constants/`: 常量定义

## 开发注意事项

### 数据库相关

- 使用 MyBatis Plus 进行 ORM 操作
- Mapper XML 文件位于 `resources/mapper/` 目录
- 测试框架: JUnit 5 + Spring Boot Test

### 缓存使用

- Redis 配置在 `RedisConfig` 中
- 缓存 Key 常量定义在各模块的 `*CacheConst` 类中
- 支持 `@Cacheable`、`@CacheEvict` 注解

### API 接口

- 统一返回格式: `ResponseDTO<T>`
- 异常处理: `GlobalExceptionHandler`
- API 文档: 启动后访问 `/doc.html`

### 前端开发规范

- 组件命名采用 PascalCase
- 文件命名采用 kebab-case
- 常量定义在 `constants/` 目录下
- API 调用统一使用 `api/` 目录下的封装方法

## 编码规范

### 通用编码规范

#### 总体原则

- **满足业务需要**：代码必须实现业务功能
- **清晰明了**：让任何人都能看懂代码
- **尽可能简洁**：在保证清晰的前提下，代码越少越好
- **复用性和模块化**：提高代码的复用性和模块化程度

#### 命名规范

##### 英文单词要求

- 使用正确的英文单词，禁止使用拼音
- 使用常用词汇
- 命名要符合业务含义

##### 合理区分名词和动词

- **名词**：用于类、接口、枚举、数据库表名等
- **动词**：用于方法名

##### 函数命名规范

- 查询多条数据统一使用 `query` 前缀
- 查询单条数据统一使用 `get` 前缀
- 新增数据统一使用 `add` 前缀
- 更新数据统一使用 `update` 前缀
- 删除数据统一使用 `delete` 前缀

#### 注释规范

##### 通用规范

- `<AUTHOR>
- `@Date` 当前时间
- 不要 `@Copyright`

##### 方法注释

方法要尽量通过方法名自解释，不要写无用、信息冗余的方法头，不要写空有格式的方法头注释。

方法头注释内容可选，但不限于：功能说明、返回值，用法、算法实现等等。尤其是对外的方法接口声明，其注释，应当将重要、有用的信息表达清楚。

##### 逻辑分块注释

使用分隔线风格的注释来分块重要逻辑：

```javascript
function startLuckDraw(luckDrawDTO) {
  // -------------- 1、校验抽奖活动基本信息 ------------------------
  
  // -------------- 2、新增抽奖记录 -------------------------------
  
  // -------------- 3、如果需要消耗积分，则扣除积分 -----------------
  
  // -------------- 4、获取奖品信息，开始抽奖 ----------------------
}
```

### Java 编码规范

#### 目录结构规范

```bash
src                               源码目录
|-- common                            各个项目的通用类库
|-- config                            项目的配置信息
|-- constant                          全局公共常量
|-- handler                           全局处理器
|-- interceptor                       全局连接器
|-- listener                          全局监听器
|-- module                            各个业务
|-- |--- employee                         员工模块
|-- |--- role                             角色模块
|-- |--- login                            登录模块
|-- third                             三方服务，比如redis, oss，微信sdk等等
|-- util                              全局工具类
|-- Application.java                  启动类
```

#### 包名规范

全部采用小写方式，以中划线分隔。

```bash
# 正例
mall-management-system
order-service-client
user-api

# 反例
mall_management-system
mallManagementSystem
orderServiceClient
```

#### JavaBean 命名规范

##### JavaBean 整体要求

- 不得有任何的业务逻辑或者计算
- 基本数据类型必须使用包装类型（`Integer`, `Double`、`Boolean` 等）
- 不允许有任何的默认值
- 每个属性必须添加注释，并且必须使用多行注释
- 必须使用 `lombok` 简化 `getter/setter` 方法
- 建议对象使用 `lombok` 的 `@Builder` ，`@NoArgsConstructor`，同时使用这两个注解

##### JavaBean 名字划分

- `XxxEntity` 数据库持久对象
- `XxxVO` 返回前端对象
- `XxxForm` 前端请求对象
- `XxxDTO` 数据传输对象
- `XxxBO` 内部处理对象

##### 数据对象 XxxxEntity 要求

- 以 `Entity` 为结尾
- Xxxx 与数据库表名保持一致
- 类中字段要与数据库字段保持一致，不能缺失或者多余
- 类中的每个字段添加注释，并与数据库注释保持一致
- 不允许有组合
- 项目内的日期类型必须统一，使用 `java.time.LocalDateTime` 或者 `java.time.LocalDate`

##### Boolean 类型命名规范

`boolean` 类型的类属性和数据表字段都统一使用 `flag` 结尾

#### 方法参数规范

无论是 `controller`，`service`，`manager`，`dao` 亦或是其他 class 的代码，每个方法最多 5 个参数，如果超出 5 个参数的话，要封装成 `javabean` 对象。

#### MVC 规范

##### Controller 层

- 只允许在 `method` 上添加 `RequestMapping` 注解，不允许加在 `class` 上
- 只能使用 `get/post` 方法。url 命名遵循：`/业务模块/子模块/动作`
- `controller` 每个方法要保持简洁，不做任何的业务逻辑操作
- 只能在 `controller` 层获取当前请求用户，并传递给 `service` 层

##### Service 层

- 合理拆分 `service` 业务，不建议 `service` 文件行数太大
- 谨慎使用 `@Transactional` 事务注解
- `@Transactional` 注解内的 `rollbackFor` 值必须使用异常 `Exception.class`
- 将数据在 `service` 层准备好，然后传递给 `manager` 层

##### Manager 层

- 对第三方平台封装的层，预处理返回结果及转化异常信息
- 对 `Service` 层通用能力的下沉，如缓存方案、中间件通用处理
- 与 `DAO` 层交互，对多个 `DAO` 的组合复用

##### DAO 层

- 优先使用 `mybatis-plus` 框架
- 所有 `Dao` 继承自 `BaseMapper`
- 禁止使用 `Mybatis-plus` 的 `Wrapper` 条件构建器
- 禁止直接在 `mybatis.xml` 中写死常量，应从 `dao` 中传入到 `xml` 中

#### 函数顺序

函数顺序按照以下顺序排列：

1. 查询类方法
2. 新增类方法
3. 修改类方法
4. 删除类方法
5. 其他类方法

### Vue3 编码规范

#### 整体规范

- 代码只用 JavaScript 进行开发

#### 命名规范

##### 项目命名

全部采用小写方式，以中划线分隔。

```bash
# 正例
smart-admin
vue-project

# 反例
mall_management-system
mallManagementSystem
```

##### 目录、文件命名

目录、文件名均以小写方式，以中划线分隔。

```bash
# 正例
/head-search/
/shopping-car/
smart-logo.png
role-form.vue

# 反例
/headSearch/
smartLogo.png
RoleForm.vue
```

#### 代码风格

##### 引号和分号

- HTML 标签属性使用双引号
- JavaScript 字符串使用单引号
- JavaScript 语句必须以分号结尾

#### Vue3 组合式 API 规范

##### 使用 setup 语法糖

- 组件必须使用 `setup` 语法糖
- 全局都要使用 `setup` 语法糖

##### Vue3 组合式 Composition API 规范

组件内必须使用模块化思想，把代码进行拆分，将相关的变量和代码写到一起，并使用行注释进行分块。

##### 模板引用变量 Ref

- 使用 `ref` 方法，参数为空进行声明变量
- 变量必须以 `Ref` 为结尾
- `template` 中的 `ref` 也必须以 `Ref` 为结尾

##### 变量和方法的注释

- 变量必须都加上注释
- 方法必须加上 jsdoc 注释

##### 通用组件使用

- `loading` 组件必须使用 `SmartLoading` 组件
- 异常捕获只需要使用 `smartSentry.captureError(error)` 进行捕获

#### Vue3 组件规范

##### 组件文件命名

- 组件文件名应该为 pascal-case 格式
- 和父组件紧密耦合的子组件应该以父组件名作为前缀命名

##### 组件属性

组件属性较多，应该主动换行。

##### 模板中表达式

组件模板应该只包含简单的表达式，复杂的表达式则应该重构为计算属性或方法。

##### 标签顺序

单文件组件应该总是让标签顺序保持为 `<template>`、`<script>`、`<style>`

#### Vue Router 规范

##### path 和 name 命名规范

- `path` 采用 `kebab-case` 命名规范
- `path` 必须以 `/` 开头，即使是 `children` 里的 `path` 也要以 `/` 开头
- `name` 命名规范采用 `PascalCase` 命名规范且和 `component` 组件名保持一致

##### 页面传参

使用路由参数进行传参，即 `{query:param}`

#### Vue 目录结构规范

```bash
src/                               源码目录
├── api/                              所有api接口
├── assets/                           静态资源，images, icons, styles等
├── components/                       公用组件
├── config/                           配置信息
├── constants/                        常量信息，项目所有Enum, 全局常量等
├── directives/                       自定义指令
├── i18n/                             国际化
├── lib/                              外部引用的插件存放及修改文件
├── mock/                             模拟接口，临时存放
├── plugins/                          插件，全局使用
├── router/                           路由，统一管理
├── store/                            vuex, 统一管理
├── theme/                            自定义样式主题
├── utils/                            工具类
├── views/                            视图目录
```

##### API 目录结构规范

- API 文件要以 `api` 为结尾，比如 `employee-api.js`、`login-api.js`
- API 文件必须导出对象必须以 `Api` 为结尾，如：`employeeApi`、`noticeApi`
- API 中以一个对象将方法包裹
- API 中的注释，必须和后端 swagger 文档保持一致，同时保留后端作者

##### Assets 目录

assets 为静态资源，里面存放 images, styles, icons 等静态资源，静态资源命名格式为 `kebab-case`

##### Components 目录

此目录应按照组件进行目录划分，目录命名为 `kebab-case`，一个组件必须一个单独的目录

##### Constants 目录

- 常量文件要以 `const` 为结尾，比如 `login-const.js`、`file-const.js`
- 变量要：大写下划线，比如 `LOGIN_RESULT_ENUM`、`LOGIN_SUCCESS`、`LOGIN_FAIL`
- 如果是枚举，变量必须以 `ENUM` 为结尾，如：`LOGIN_RESULT_ENUM`、`CODE_FRONT_COMPONENT_ENUM`

##### Router 与 Store 目录

- `router` 尽量按照 views 中的结构保持一致
- `store` 按照业务进行拆分不同的 js 文件

##### Views 目录

- 如果是列表页面，要以 `list` 为结尾，如 `role-list.vue`、`cache-list.vue`
- 如果是表单页面，要以 `form` 为结尾，如 `role-form.vue`、`notice-add-form.vue`
- 如果是 modal 弹窗，要以 `modal` 为结尾，如表单弹窗 `role-form-modal.vue`，详情 `role-detail-modal.vue`
- 如果是 drawer 抽屉页面，要同上以 `drawer` 为结尾

## 项目启动说明

### 数据库初始化

1. 执行 `sql/smart_admin_v3.sql` 创建基础表结构
2. 执行 `sql/server_management.sql` 创建服务器管理表

### 启动顺序

1. 确保 Redis 服务运行
2. 配置数据库连接 (`resources/{env}/application.yaml`)
3. 启动后端服务 (`mvn spring-boot:run`)
4. 启动前端服务 (`npm run dev`)
5. 访问 `http://localhost:3000` (前端) 和 `http://localhost:8080/doc.html` (API 文档)
